<svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
  <circle cx="40" cy="40" r="40" fill="rgba(34, 209, 238, 0.1)">
    <animate attributeName="opacity" values="0.1;0.15;0.1" dur="2s" repeatCount="indefinite"/>
  </circle>
  
  <g transform="translate(12, 12)">
    <circle cx="28" cy="28" r="24" stroke="#22d1ee" stroke-width="2.5" opacity="0.2"/>
    
    <!-- 内部填充 -->
    <rect x="14" y="14" width="28" height="28" fill="#22d1ee" opacity="0.1" rx="4">
      <animate attributeName="opacity" values="0.1;0.2;0.1" dur="2s" repeatCount="indefinite"/>
    </rect>
    
    <!-- 上箭头 -->
    <path d="M38 18l6 6-6 6" stroke="#22d1ee" stroke-width="2.5" stroke-linecap="round">
      <animate attributeName="stroke-dasharray" values="0 24;24 24" dur="0.6s" fill="freeze"/>
      <animate attributeName="transform" values="translate(0,0);translate(2,0);translate(0,0)" dur="2s" repeatCount="indefinite"/>
    </path>
    <path d="M44 24H12" stroke="#22d1ee" stroke-width="2.5" stroke-linecap="round">
      <animate attributeName="stroke-dasharray" values="0 32;32 32" dur="0.4s" begin="0.6s" fill="freeze"/>
    </path>
    
    <!-- 下箭头 -->
    <path d="M18 32l-6 6 6 6" stroke="#22d1ee" stroke-width="2.5" stroke-linecap="round">
      <animate attributeName="stroke-dasharray" values="0 24;24 24" dur="0.6s" begin="1s" fill="freeze"/>
      <animate attributeName="transform" values="translate(0,0);translate(-2,0);translate(0,0)" dur="2s" repeatCount="indefinite"/>
    </path>
    <path d="M12 38h32" stroke="#22d1ee" stroke-width="2.5" stroke-linecap="round">
      <animate attributeName="stroke-dasharray" values="0 32;32 32" dur="0.4s" begin="1.6s" fill="freeze"/>
    </path>
  </g>

  <circle cx="40" cy="40" r="38" stroke="url(#gradient)" stroke-width="2" opacity="0">
    <animate attributeName="opacity" values="0;0.6;0" dur="2s" begin="mouseover" fill="freeze"/>
  </circle>

  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#22d1ee;stop-opacity:0.5"/>
      <stop offset="100%" style="stop-color:#22d1ee;stop-opacity:0"/>
    </linearGradient>
  </defs>
</svg> 