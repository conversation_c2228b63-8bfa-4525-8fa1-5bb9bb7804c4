<svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
  <circle cx="40" cy="40" r="40" fill="rgba(34, 209, 238, 0.1)">
    <animate attributeName="opacity" values="0.1;0.15;0.1" dur="2s" repeatCount="indefinite"/>
  </circle>
  
  <g transform="translate(12, 12)">
    <circle cx="28" cy="28" r="24" stroke="#22d1ee" stroke-width="2.5" opacity="0.2"/>
    
    <!-- 内部填充 -->
    <path d="M28 8c11 0 20 9 20 20v8c0 5.5-4.5 10-10 10H18c-5.5 0-10-4.5-10-10v-8c0-11 9-20 20-20z" 
          fill="#22d1ee" opacity="0.1">
      <animate attributeName="opacity" values="0.1;0.2;0.1" dur="2s" repeatCount="indefinite"/>
    </path>
    
    <!-- 耳机主体 -->
    <path d="M28 8c11 0 20 9 20 20v8c0 5.5-4.5 10-10 10H18c-5.5 0-10-4.5-10-10v-8c0-11 9-20 20-20z" 
          stroke="#22d1ee" stroke-width="2.5" stroke-linecap="round">
      <animate attributeName="stroke-dasharray" values="0 150;150 150" dur="1.2s" fill="freeze"/>
    </path>
    
    <!-- 笑脸动画 -->
    <path d="M20 24a4 4 0 0 0 16 0" stroke="#22d1ee" stroke-width="2.5" stroke-linecap="round">
      <animate attributeName="d" 
               values="M20 24a4 4 0 0 0 16 0;M20 28a4 4 0 0 0 16 0;M20 24a4 4 0 0 0 16 0" 
               dur="3s" 
               repeatCount="indefinite"/>
    </path>
    
    <!-- 耳机装饰 -->
    <path d="M8 28c0-11 9-20 20-20s20 9 20 20" 
          stroke="#22d1ee" stroke-width="2.5" stroke-linecap="round" opacity="0.3">
      <animate attributeName="opacity" values="0.3;0.6;0.3" dur="2s" repeatCount="indefinite"/>
    </path>
  </g>

  <circle cx="40" cy="40" r="38" stroke="url(#gradient)" stroke-width="2" opacity="0">
    <animate attributeName="opacity" values="0;0.6;0" dur="2s" begin="mouseover" fill="freeze"/>
  </circle>

  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#22d1ee;stop-opacity:0.5"/>
      <stop offset="100%" style="stop-color:#22d1ee;stop-opacity:0"/>
    </linearGradient>
  </defs>
</svg> 