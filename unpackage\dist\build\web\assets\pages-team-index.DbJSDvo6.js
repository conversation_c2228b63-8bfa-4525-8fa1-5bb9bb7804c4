import{H as s,s as a,n as t,f as e,h as i,w as o,z as l,i as n,o as c,j as r,m as d,t as m,p as u,l as f,q as g,F as h,k as _,x as C,I as p,y}from"./index-GgeQ7060.js";import{_ as b}from"./uni-icons.CMMZY0R_.js";import{r as k}from"./uni-app.es.COHFzCZB.js";import{r as T}from"./request.DgxtsFy5.js";import{_ as R}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.B6QF5Ba_.js";const v=R({data:()=>({statusBarHeight:0,navBarHeight:44,navPaddingTop:44,activeTab:0,teamStats:{todayRecommend:0,directTotal:0,validDirect:0,teamTotal:0},recordList:[],page:1,pageSize:10,total:0,loading:!1,showAssignDialog:!1,selectedUser:{},assignRate:"",myCommissionRate:0,showCustomConfirm:!1,confirmContent:"",confirmCallback:null}),async created(){s(),await this.getMyCommissionRate(),await this.getTeamStats(),await this.getRecordList(!0)},methods:{async getTeamStats(){try{const s=await T({url:"/api/user/team-stats",method:"GET"}),a=s.data||s;(200===s.code||s.success)&&a&&(this.teamStats=a)}catch(s){a({title:"获取团队数据失败",icon:"none"})}},async getMyCommissionRate(){try{const s=await T({url:"/api/user/info",method:"GET"});200===s.code&&s.data&&(this.myCommissionRate=s.data.commissionRate||0)}catch{}},async getRecordList(s=!1){this.loading=!0,s&&(this.page=1);const a=["direct","valid","today"][this.activeTab];try{const t=await T({url:"/api/user/team-records",method:"GET",data:{type:a,page:this.page,size:this.pageSize}});200===t.code&&t.data&&(this.recordList=s?t.data.records:this.recordList.concat(t.data.records),this.total=t.data.total)}finally{this.loading=!1}},onTabChange(s){this.activeTab=s,this.getRecordList(!0)},loadMore(){this.recordList.length<this.total&&!this.loading&&(this.page++,this.getRecordList())},formatTime(s){if(!s)return"";const a=new Date(s);return`${a.getFullYear()}-${(a.getMonth()+1).toString().padStart(2,"0")}-${a.getDate().toString().padStart(2,"0")} ${a.getHours().toString().padStart(2,"0")}:${a.getMinutes().toString().padStart(2,"0")}`},handleBack(){t()},canAssignCommission(s){return 1===this.activeTab&&0==s.commissionRate&&this.myCommissionRate>0},async onAssignCommission(s){await this.getMyCommissionRate(),this.canAssignCommission(s)&&(this.selectedUser=s,this.assignRate="",this.showAssignDialog=!0)},showConfirm(s,a){this.confirmContent=s,this.showCustomConfirm=!0,this.confirmCallback=a},handleConfirm(s){this.showCustomConfirm=!1,s&&"function"==typeof this.confirmCallback&&this.confirmCallback()},async confirmAssign(){const s=Number(this.assignRate);!s||s<=0?a({title:"请输入大于0的分配比例",icon:"none"}):s>this.myCommissionRate?a({title:"分配比例不能大于自己可分配佣金",icon:"none"}):this.showConfirm("一旦分配，不可再进行修改，是否确认分配？",(async()=>{try{const t=await T({url:"/api/user/assign-commission",method:"POST",data:{userNo:this.selectedUser.userNo,assignRate:s}});200===t.code?(a({title:"分配成功",icon:"success"}),this.showAssignDialog=!1,this.getRecordList(!0)):a({title:t.msg||"分配失败",icon:"none"})}catch(t){a({title:"分配失败",icon:"none"})}}))},assignInputChange(s){Number(s.detail.value)>this.myCommissionRate&&(a({title:"分配比例不能大于自己可分配佣金",icon:"none"}),this.assignRate=this.myCommissionRate)}}},[["render",function(s,a,t,T,R,v){const w=k(e("uni-icons"),b),S=n,A=C,L=p,x=y;return c(),i(S,{class:"team-container",style:l({paddingTop:R.navPaddingTop+"px"})},{default:o((()=>[r(S,{class:"custom-navbar",style:l({paddingTop:R.statusBarHeight+"px"})},{default:o((()=>[r(S,{class:"navbar-content"},{default:o((()=>[r(S,{class:"left-area",onClick:v.handleBack},{default:o((()=>[r(w,{type:"left",size:"20",color:"#fff"})])),_:1},8,["onClick"]),r(A,{class:"page-title"},{default:o((()=>[d("我的团队")])),_:1}),r(S,{class:"right-area"})])),_:1})])),_:1},8,["style"]),r(S,{class:"team-stats"},{default:o((()=>[r(S,{class:"stats-row"},{default:o((()=>[r(S,{class:"stats-col"},{default:o((()=>[r(A,{class:"stats-label"},{default:o((()=>[d("今日推荐")])),_:1}),r(A,{class:"stats-value"},{default:o((()=>[d(m(R.teamStats.todayRecommend),1)])),_:1})])),_:1}),r(S,{class:"stats-col"},{default:o((()=>[r(A,{class:"stats-label"},{default:o((()=>[d("直推总数")])),_:1}),r(A,{class:"stats-value"},{default:o((()=>[d(m(R.teamStats.directTotal),1)])),_:1})])),_:1}),r(S,{class:"stats-col"},{default:o((()=>[r(A,{class:"stats-label"},{default:o((()=>[d("有效直推")])),_:1}),r(A,{class:"stats-value"},{default:o((()=>[d(m(R.teamStats.validDirect),1)])),_:1})])),_:1}),r(S,{class:"stats-col"},{default:o((()=>[r(A,{class:"stats-label"},{default:o((()=>[d("我的团队")])),_:1}),r(A,{class:"stats-value"},{default:o((()=>[d(m(R.teamStats.teamTotal),1)])),_:1})])),_:1})])),_:1})])),_:1}),r(S,{class:"tab-bar"},{default:o((()=>[r(S,{class:u(["tab",{active:0===R.activeTab}]),onClick:a[0]||(a[0]=s=>v.onTabChange(0))},{default:o((()=>[d("直推记录")])),_:1},8,["class"]),r(S,{class:u(["tab",{active:1===R.activeTab}]),onClick:a[1]||(a[1]=s=>v.onTabChange(1))},{default:o((()=>[d("有效记录")])),_:1},8,["class"]),r(S,{class:u(["tab",{active:2===R.activeTab}]),onClick:a[2]||(a[2]=s=>v.onTabChange(2))},{default:o((()=>[d("今日记录")])),_:1},8,["class"])])),_:1}),r(S,{class:"record-card"},{default:o((()=>[(c(!0),f(h,null,g(R.recordList,(s=>(c(),i(S,{key:s.userNo,class:"record-item"},{default:o((()=>[r(A,{class:"record-uid"},{default:o((()=>[d(m(s.userNo),1)])),_:2},1024),r(A,{class:"record-username"},{default:o((()=>[d(m(s.username),1)])),_:2},1024),1===R.activeTab?(c(),i(A,{key:0,class:u(["record-commission",{clickable:v.canAssignCommission(s)}]),onClick:a=>v.onAssignCommission(s)},{default:o((()=>[d(" 佣金比例: "+m(s.commissionRate)+"% ",1)])),_:2},1032,["class","onClick"])):(c(),i(A,{key:1,class:"record-time"},{default:o((()=>[d(m(v.formatTime(s.createTime)),1)])),_:2},1024))])),_:2},1024)))),128)),R.loading?(c(),i(S,{key:0,class:"loading-text"},{default:o((()=>[d("加载中...")])),_:1})):_("",!0),R.loading||0!==R.recordList.length?_("",!0):(c(),i(S,{key:1,class:"empty-text"},{default:o((()=>[d("暂无数据")])),_:1})),!R.loading&&R.recordList.length<R.total?(c(),i(S,{key:2,class:"load-more",onClick:v.loadMore},{default:o((()=>[d("加载更多")])),_:1},8,["onClick"])):_("",!0)])),_:1}),R.showAssignDialog?(c(),i(S,{key:0,class:"assign-dialog-mask"},{default:o((()=>[r(S,{class:"assign-dialog"},{default:o((()=>[r(S,{class:"assign-title"},{default:o((()=>[d("分配佣金比例")])),_:1}),r(S,{class:"assign-info-row"},{default:o((()=>[r(A,{class:"assign-label"},{default:o((()=>[d("我的佣金比例：")])),_:1}),r(A,{class:"assign-my-rate"},{default:o((()=>[d(m(R.myCommissionRate)+"%",1)])),_:1})])),_:1}),r(S,{class:"assign-info-row"},{default:o((()=>[r(A,{class:"assign-label"},{default:o((()=>[d("分配给：")])),_:1}),r(A,{class:"assign-userid",title:R.selectedUser.userNo},{default:o((()=>[d(m(R.selectedUser.userNo),1)])),_:1},8,["title"])])),_:1}),r(S,{class:"assign-info-row"},{default:o((()=>[r(A,{class:"assign-label"},{default:o((()=>[d("昵称：")])),_:1}),r(A,{class:"assign-username",title:R.selectedUser.username},{default:o((()=>[d(m(R.selectedUser.username&&R.selectedUser.username.length>6?R.selectedUser.username.slice(0,6)+"...":R.selectedUser.username),1)])),_:1},8,["title"])])),_:1}),r(L,{class:"assign-input",type:"number",modelValue:R.assignRate,"onUpdate:modelValue":a[3]||(a[3]=s=>R.assignRate=s),placeholder:"请输入分配比例",min:"0",max:R.myCommissionRate,onInput:v.assignInputChange},null,8,["modelValue","max","onInput"]),r(S,{class:"assign-btns"},{default:o((()=>[r(x,{class:"assign-btn",onClick:v.confirmAssign},{default:o((()=>[d("分配")])),_:1},8,["onClick"]),r(x,{class:"assign-btn cancel",onClick:a[4]||(a[4]=s=>R.showAssignDialog=!1)},{default:o((()=>[d("取消")])),_:1})])),_:1})])),_:1})])),_:1})):_("",!0),R.showCustomConfirm?(c(),i(S,{key:1,class:"custom-confirm-mask"},{default:o((()=>[r(S,{class:"custom-confirm-dialog"},{default:o((()=>[r(S,{class:"custom-confirm-title"},{default:o((()=>[d("确认分配")])),_:1}),r(S,{class:"custom-confirm-content"},{default:o((()=>[d(m(R.confirmContent),1)])),_:1}),r(S,{class:"custom-confirm-btns"},{default:o((()=>[r(x,{class:"custom-btn cancel",onClick:a[5]||(a[5]=s=>v.handleConfirm(!1))},{default:o((()=>[d("取消")])),_:1}),r(x,{class:"custom-btn confirm",onClick:a[6]||(a[6]=s=>v.handleConfirm(!0))},{default:o((()=>[d("确认分配")])),_:1})])),_:1})])),_:1})])),_:1})):_("",!0)])),_:1},8,["style"])}],["__scopeId","data-v-0398ff1e"]]);export{v as default};
