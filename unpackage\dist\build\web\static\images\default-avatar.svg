<svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景圆圈 -->
  <circle cx="60" cy="60" r="60" fill="rgba(34, 209, 238, 0.1)">
    <animate attributeName="opacity" values="0.1;0.15;0.1" dur="3s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 外圈装饰 -->
  <circle cx="60" cy="60" r="56" stroke="#22d1ee" stroke-width="2" opacity="0.3">
    <animate attributeName="stroke-dasharray" values="0 360;360 360" dur="2s" fill="freeze"/>
    <animateTransform attributeName="transform" 
                    type="rotate" 
                    from="0 60 60" 
                    to="360 60 60" 
                    dur="20s" 
                    repeatCount="indefinite"/>
  </circle>
  
  <!-- 头像主体 -->
  <g transform="translate(30, 25)">
    <!-- 头部轮廓 -->
    <circle cx="30" cy="30" r="24" fill="#22d1ee" opacity="0.2"/>
    
    <!-- 身体轮廓 -->
    <path d="M0 65c0-16.6 13.4-30 30-30s30 13.4 30 30" 
          stroke="#22d1ee" 
          stroke-width="2" 
          opacity="0.3">
      <animate attributeName="stroke-dasharray" values="0 100;100 100" dur="1s" fill="freeze"/>
    </path>
    
    <!-- 动态波纹效果 -->
    <circle cx="30" cy="30" r="28" stroke="#22d1ee" stroke-width="2" opacity="0.1">
      <animate attributeName="r" values="28;32;28" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.1;0.2;0.1" dur="3s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- 装饰点 -->
  <circle cx="85" cy="35" r="4" fill="#22d1ee" opacity="0.5">
    <animate attributeName="opacity" values="0.5;0.8;0.5" dur="2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="35" cy="85" r="4" fill="#22d1ee" opacity="0.5">
    <animate attributeName="opacity" values="0.5;0.8;0.5" dur="2s" begin="1s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 悬停效果 -->
  <circle cx="60" cy="60" r="58" stroke="url(#avatar-gradient)" stroke-width="2" opacity="0">
    <animate attributeName="opacity" values="0;0.6;0" dur="2s" begin="mouseover" fill="freeze"/>
  </circle>
  
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="avatar-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#22d1ee;stop-opacity:0.5"/>
      <stop offset="100%" style="stop-color:#22d1ee;stop-opacity:0"/>
    </linearGradient>
  </defs>
</svg> 