<svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
  <circle cx="40" cy="40" r="40" fill="rgba(34, 209, 238, 0.1)">
    <animate attributeName="opacity" values="0.1;0.15;0.1" dur="2s" repeatCount="indefinite"/>
  </circle>
  
  <g transform="translate(12, 12)">
    <circle cx="28" cy="28" r="24" stroke="#22d1ee" stroke-width="2.5" opacity="0.2"/>
    
    <!-- 内部填充 -->
    <rect x="8" y="16" width="36" height="24" fill="#22d1ee" opacity="0.1" rx="3">
      <animate attributeName="opacity" values="0.1;0.2;0.1" dur="2s" repeatCount="indefinite"/>
    </rect>
    
    <!-- 电池主体 -->
    <rect x="8" y="16" width="36" height="24" rx="3" stroke="#22d1ee" stroke-width="2.5">
      <animate attributeName="stroke-dasharray" values="0 120;120 120" dur="1s" fill="freeze"/>
    </rect>
    
    <!-- 电池头 -->
    <path d="M44 22h4v12h-4v-12z" fill="#22d1ee">
      <animate attributeName="opacity" values="0;1;0.8;1" dur="2s" begin="0.8s" fill="freeze"/>
    </path>
    
    <!-- 电量动画 -->
    <rect x="11" y="19" width="30" height="18" fill="#22d1ee" opacity="0.3">
      <animate attributeName="width" values="0;30;28;30" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.2;0.4;0.2" dur="2s" repeatCount="indefinite"/>
    </rect>
  </g>

  <circle cx="40" cy="40" r="38" stroke="url(#gradient)" stroke-width="2" opacity="0">
    <animate attributeName="opacity" values="0;0.6;0" dur="2s" begin="mouseover" fill="freeze"/>
  </circle>

  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#22d1ee;stop-opacity:0.5"/>
      <stop offset="100%" style="stop-color:#22d1ee;stop-opacity:0"/>
    </linearGradient>
  </defs>
</svg> 