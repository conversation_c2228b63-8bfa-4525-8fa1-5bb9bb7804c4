import{s as a,d as e,e as t,h as l,w as s,i as o,o as r,l as i,q as d,F as n,j as c,m as u,t as f,x as g,v as A,y as I}from"./index-GgeQ7060.js";import{r as h}from"./request.DgxtsFy5.js";import{c as m}from"./index.B6QF5Ba_.js";import{_ as E}from"./_plugin-vue_export-helper.BCo6x5W8.js";const y=E({data:()=>({leaderList:[],loading:!0,baseURL:m.apiBaseUrl,page:0,size:10,user:null,todayProfit:0,totalProfit:0}),computed:{isFollowing(){return this.user&&1===this.user.isFollowing},safeLeaderList(){return Array.isArray(this.leaderList)?this.leaderList.filter((a=>a&&"object"==typeof a)):[]},safeTotalProfit(){const a=(Number(this.totalProfit)||0).toFixed(2);return console.log("计算 safeTotalProfit:",this.totalProfit,"->",a),a},safeTodayProfit(){const a=(Number(this.todayProfit)||0).toFixed(2);return console.log("计算 safeTodayProfit:",this.todayProfit,"->",a),a}},onShow(){this.$nextTick((()=>{this.initPage()}))},onLoad(){this.initPage()},onUnload(){this.leaderList=[],this.user=null,this.loading=!1},methods:{async initPage(){try{this.loading=!0,await Promise.all([this.loadUserInfo(),this.loadLeaderList(),this.loadProfitSummary()]),await this.$nextTick()}catch(e){console.error("页面初始化失败:",e),a({title:"页面加载失败，请重试",icon:"none"})}finally{this.loading=!1,await this.$nextTick()}},async loadUserInfo(){try{const a=await h({url:"/api/user/info",method:"GET"});a&&a.data&&(this.user=a.data)}catch(a){this.user=null}},getImageUrl(a){if(!a)return"/static/default-avatar.png";try{return a.startsWith("/static/")?a:a.startsWith("/")?`${this.baseURL}${a}`:a}catch(e){return console.error("获取图片URL失败:",e),"/static/default-avatar.png"}},handleImageError(a){console.log("图片加载失败:",a),a.target&&(a.target.src="/static/default-avatar.png")},async loadLeaderList(){try{const a=await h({url:"/api/leader/summary",method:"GET"});a&&Array.isArray(a.data)?(this.leaderList=a.data,await this.$nextTick()):this.leaderList=[]}catch(a){throw console.error("加载带单员列表失败:",a),this.leaderList=[],a}},async loadProfitSummary(){try{console.log("开始加载收益汇总数据...");const a=await h({url:"/api/copy/profit/summary",method:"GET"});console.log("收益汇总接口响应:",a),a&&(0===a.code||200===a.code)&&a.data?(this.todayProfit=a.data.today_profit||0,this.totalProfit=a.data.total_profit||0,console.log("收益数据更新成功:",{todayProfit:this.todayProfit,totalProfit:this.totalProfit})):(console.warn("收益数据格式异常:",a),this.todayProfit=0,this.totalProfit=0)}catch(a){console.error("加载收益汇总失败:",a),this.todayProfit=0,this.totalProfit=0}},async refreshProfitData(){console.log("手动刷新收益数据..."),await this.loadProfitSummary(),a({title:"数据已刷新",icon:"success"})},handleFollow(t){const l=Number(t.min_follow_amount),s=Number(t.max_follow_amount),o=Number(this.user&&this.user.copyTradeBalance);isNaN(o)?a({title:"无法获取跟单账户余额",icon:"none"}):o<l?a({title:"跟单账户余额低于最低跟单金额",icon:"none"}):s>0&&o>s?a({title:"跟单账户余额高于最高跟单金额",icon:"none"}):e({title:"提示",content:"是否确认一键跟单？",confirmText:"确认",cancelText:"取消",success:e=>{e.confirm&&h({url:"/api/leader/follow",method:"POST",data:{leaderId:t.id}}).then((async()=>{a({title:"跟单成功",icon:"success"}),await this.loadUserInfo(),await this.loadLeaderList(),await this.$nextTick()})).catch((e=>{a({title:e.message||"操作失败",icon:"none"})}))}})},handleUnfollow(){e({title:"提示",content:"是否确认取消跟单？",confirmText:"确认",cancelText:"取消",success:e=>{e.confirm&&h({url:"/api/leader/unfollow",method:"POST"}).then((async()=>{a({title:"已取消跟单",icon:"success"}),await this.loadUserInfo(),await this.loadLeaderList(),await this.$nextTick()})).catch((e=>{a({title:e.message||"操作失败",icon:"none"})}))}})},goToMyCopy(){t({url:"/pages/copy/mycopy"})}}},[["render",function(a,e,t,h,m,E){const y=o,C=g,k=A,Q=I;return r(),l(y,{class:"robot-follow-container"},{default:s((()=>[m.loading?(r(),l(y,{key:0},{default:s((()=>[(r(),i(n,null,d(3,(a=>c(y,{class:"skeleton-card",key:a,style:{"margin-bottom":"24rpx"}},{default:s((()=>[c(y,{class:"skeleton-avatar"}),c(y,{class:"skeleton-info"},{default:s((()=>[c(y,{class:"skeleton-title"}),c(y,{class:"skeleton-row"})])),_:1})])),_:2},1024))),64))])),_:1})):(r(),l(y,{key:1},{default:s((()=>[c(y,{class:"copy-balance-card"},{default:s((()=>[c(y,{class:"card-header"},{default:s((()=>[u("跟单账户余额")])),_:1}),c(y,{class:"card-subtitle"},{default:s((()=>[u("跟单净利润")])),_:1}),c(y,{class:"card-balance-row-flex"},{default:s((()=>[c(y,{class:"card-balance-left"},{default:s((()=>[c(y,{style:{display:"flex","align-items":"flex-end"}},{default:s((()=>[c(C,{class:"card-balance"},{default:s((()=>[u(f(E.safeTotalProfit),1)])),_:1}),c(C,{class:"card-unit"},{default:s((()=>[u("USDT")])),_:1})])),_:1}),c(y,{class:"card-today",style:{"margin-top":"18rpx"}},{default:s((()=>[u("今日收益 "),c(C,{class:"card-today-value"},{default:s((()=>[u(f(E.safeTodayProfit),1)])),_:1})])),_:1})])),_:1}),c(y,{class:"card-balance-arrow-box"},{default:s((()=>[c(k,{class:"card-arrow-img",src:"data:image/png;base64,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",mode:"widthFix",onClick:E.goToMyCopy},null,8,["onClick"])])),_:1})])),_:1})])),_:1}),E.safeLeaderList.length>0?(r(),l(y,{key:0,class:"leader-list"},{default:s((()=>[(r(!0),i(n,null,d(E.safeLeaderList,((a,e)=>(r(),l(y,{class:"leader-card",key:`leader-${a.id||a.username||e}`},{default:s((()=>[c(y,{class:"leader-card-header"},{default:s((()=>[c(k,{src:E.getImageUrl(a.avatar),class:"leader-avatar",onError:E.handleImageError},null,8,["src","onError"]),c(y,{class:"leader-userinfo"},{default:s((()=>[c(y,{class:"leader-name"},{default:s((()=>[u(f(a.username||"未知用户"),1)])),_:2},1024),c(y,{class:"leader-level-inline"},{default:s((()=>[c(C,{class:"type-label"},{default:s((()=>[u(f(0===a.copy_type?"短线":1===a.copy_type?"中线":"长线"),1)])),_:2},1024),c(y,{class:"follower-inline"},{default:s((()=>[c(k,{class:"follower-icon-img",src:"/assets/p-eSNEPaIe.png",mode:"aspectFit",onError:E.handleImageError},null,8,["onError"]),c(C,{class:"follower-num"},{default:s((()=>[u(f(a.follower_count||0),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024),c(Q,{class:"follow-btn",disabled:E.isFollowing&&m.user&&m.user.leaderId!==a.id||m.user&&1===m.user.isLeader,onClick:e=>m.user&&1===m.user.isFollowing&&m.user.leaderId===a.id?E.handleUnfollow():E.handleFollow(a)},{default:s((()=>[u(f(m.user&&1===m.user.isFollowing&&m.user.leaderId===a.id?"一键取消":"一键跟单"),1)])),_:2},1032,["disabled","onClick"])])),_:2},1024),c(y,{class:"leader-card-body-compact3"},{default:s((()=>[c(y,{class:"leader-row-compact3"},{default:s((()=>[c(y,null,{default:s((()=>[c(y,{class:"leader-label"},{default:s((()=>[u("储备金")])),_:1}),c(y,{class:"leader-value"},{default:s((()=>[u(f(a.reserve_amount||"0"),1)])),_:2},1024)])),_:2},1024),c(y,null,{default:s((()=>[c(y,{class:"leader-label"},{default:s((()=>[u("成交笔数")])),_:1}),c(y,{class:"leader-value"},{default:s((()=>[u(f(a.order_count||"0"),1)])),_:2},1024)])),_:2},1024),c(y,null,{default:s((()=>[c(y,{class:"leader-label"},{default:s((()=>[u("总盈利")])),_:1}),c(y,{class:"leader-value"},{default:s((()=>[u(f(a.total_profit||"0"),1)])),_:2},1024)])),_:2},1024)])),_:2},1024),c(y,{class:"leader-row-compact3"},{default:s((()=>[c(y,null,{default:s((()=>[c(y,{class:"leader-label"},{default:s((()=>[u("最低跟单金额")])),_:1}),c(y,{class:"leader-value"},{default:s((()=>[u(f(a.min_follow_amount||"0"),1)])),_:2},1024)])),_:2},1024),c(y,null,{default:s((()=>[c(y,{class:"leader-label"},{default:s((()=>[u("最高跟单金额")])),_:1}),c(y,{class:"leader-value"},{default:s((()=>[u(f(0===Number(a.max_follow_amount||0)?"不限":a.max_follow_amount||"0"),1)])),_:2},1024)])),_:2},1024),c(y,null,{default:s((()=>[c(y,{class:"leader-label"},{default:s((()=>[u("交易周期")])),_:1}),c(y,{class:"leader-value",style:{"font-weight":"bold"}},{default:s((()=>[u(f(Number(a.lock_time||0)+"天"),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128))])),_:1})):(r(),l(y,{key:1,class:"empty-state"},{default:s((()=>[c(C,{class:"empty-text"},{default:s((()=>[u("暂无带单员数据")])),_:1})])),_:1}))])),_:1}))])),_:1})}],["__scopeId","data-v-cac8e28b"]]);export{y as default};
