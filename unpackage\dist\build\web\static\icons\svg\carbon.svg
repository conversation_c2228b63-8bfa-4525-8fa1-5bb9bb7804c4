<svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景圆圈 -->
  <circle cx="40" cy="40" r="40" fill="rgba(34, 209, 238, 0.1)">
    <animate attributeName="opacity" values="0.1;0.15;0.1" dur="2s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 主图标组 -->
  <g transform="translate(12, 12)">
    <!-- 外圈 -->
    <circle cx="28" cy="28" r="24" stroke="#22d1ee" stroke-width="2.5">
      <animateTransform attributeName="transform" 
                      type="rotate" 
                      from="0 28 28" 
                      to="360 28 28" 
                      dur="20s" 
                      repeatCount="indefinite"/>
    </circle>
    
    <!-- 内部填充效果 -->
    <circle cx="28" cy="28" r="18" fill="#22d1ee" opacity="0.1">
      <animate attributeName="r" 
               values="18;20;18" 
               dur="2s" 
               repeatCount="indefinite"/>
      <animate attributeName="opacity" 
               values="0.1;0.2;0.1" 
               dur="2s" 
               repeatCount="indefinite"/>
    </circle>
    
    <!-- 动态弧线 -->
    <path d="M38 28a10 10 0 0 1-20 0" 
          stroke="#22d1ee" 
          stroke-width="2.5" 
          stroke-linecap="round">
      <animate attributeName="d" 
               values="M38 28a10 10 0 0 1-20 0;M38 32a10 10 0 0 1-20 0;M38 28a10 10 0 0 1-20 0" 
               dur="3s" 
               repeatCount="indefinite"/>
    </path>
  </g>

  <!-- 悬停效果 -->
  <circle cx="40" cy="40" r="38" stroke="url(#gradient)" stroke-width="2" opacity="0">
    <animate attributeName="opacity" 
             values="0;0.6;0" 
             dur="2s" 
             begin="mouseover" 
             fill="freeze"/>
  </circle>

  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#22d1ee;stop-opacity:0.5"/>
      <stop offset="100%" style="stop-color:#22d1ee;stop-opacity:0"/>
    </linearGradient>
  </defs>
</svg> 