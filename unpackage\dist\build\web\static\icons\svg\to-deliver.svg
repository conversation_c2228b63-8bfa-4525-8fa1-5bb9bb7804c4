<svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
  <circle cx="40" cy="40" r="40" fill="rgba(34, 209, 238, 0.1)">
    <animate attributeName="opacity" values="0.1;0.15;0.1" dur="2s" repeatCount="indefinite"/>
  </circle>
  
  <g transform="translate(12, 12)">
    <circle cx="28" cy="28" r="24" stroke="#22d1ee" stroke-width="2.5" opacity="0.2"/>
    
    <!-- 主体卡车 -->
    <rect x="8" y="18" width="26" height="18" fill="#22d1ee" opacity="0.1" rx="2">
      <animate attributeName="opacity" values="0.1;0.2;0.1" dur="2s" repeatCount="indefinite"/>
    </rect>
    
    <!-- 车厢 -->
    <path d="M8 18h26v18H8V18z" stroke="#22d1ee" stroke-width="2.5" stroke-linecap="round">
      <animate attributeName="stroke-dasharray" values="0 88;88 88" dur="1s" fill="freeze"/>
    </path>
    
    <!-- 车头 -->
    <path d="M34 24h6l4 4v8h-10V24z" stroke="#22d1ee" stroke-width="2.5" stroke-linecap="round">
      <animate attributeName="stroke-dasharray" values="0 44;44 44" dur="1s" begin="0.3s" fill="freeze"/>
    </path>
    
    <!-- 车轮 -->
    <circle cx="16" cy="36" r="3" fill="#22d1ee">
      <animate attributeName="r" values="0;3;2.7;3" dur="1s" begin="0.6s" fill="freeze"/>
      <animateTransform attributeName="transform" type="rotate" values="0 16 36;360 16 36" dur="2s" begin="1s" repeatCount="indefinite"/>
    </circle>
    <circle cx="38" cy="36" r="3" fill="#22d1ee">
      <animate attributeName="r" values="0;3;2.7;3" dur="1s" begin="0.8s" fill="freeze"/>
      <animateTransform attributeName="transform" type="rotate" values="0 38 36;360 38 36" dur="2s" begin="1s" repeatCount="indefinite"/>
    </circle>
  </g>

  <circle cx="40" cy="40" r="38" stroke="url(#gradient)" stroke-width="2" opacity="0">
    <animate attributeName="opacity" values="0;0.6;0" dur="2s" begin="mouseover" fill="freeze"/>
  </circle>

  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#22d1ee;stop-opacity:0.5"/>
      <stop offset="100%" style="stop-color:#22d1ee;stop-opacity:0"/>
    </linearGradient>
  </defs>
</svg> 