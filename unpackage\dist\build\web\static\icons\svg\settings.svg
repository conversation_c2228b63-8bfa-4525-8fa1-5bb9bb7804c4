<svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
  <circle cx="40" cy="40" r="40" fill="rgba(34, 209, 238, 0.1)">
    <animate attributeName="opacity" values="0.1;0.15;0.1" dur="2s" repeatCount="indefinite"/>
  </circle>
  
  <g transform="translate(12, 12)">
    <circle cx="28" cy="28" r="24" stroke="#22d1ee" stroke-width="2.5" opacity="0.2"/>
    
    <!-- 内部填充 -->
    <circle cx="28" cy="28" r="20" fill="#22d1ee" opacity="0.1">
      <animate attributeName="opacity" values="0.1;0.2;0.1" dur="2s" repeatCount="indefinite"/>
      <animateTransform attributeName="transform" 
                      type="rotate" 
                      from="0 28 28" 
                      to="360 28 28" 
                      dur="20s" 
                      repeatCount="indefinite"/>
    </circle>
    
    <!-- 中心齿轮 -->
    <circle cx="28" cy="28" r="8" stroke="#22d1ee" stroke-width="2.5">
      <animate attributeName="stroke-dasharray" values="0 50;50 50" dur="0.8s" fill="freeze"/>
      <animateTransform attributeName="transform" 
                      type="rotate" 
                      from="0 28 28" 
                      to="360 28 28" 
                      dur="10s" 
                      repeatCount="indefinite"/>
    </circle>
    
    <!-- 齿轮齿 -->
    <path d="M28 4v8M28 44v8M4 28h8M44 28h8M10 10l6 6M42 42l6 6M42 10l-6 6M10 42l6 6" 
          stroke="#22d1ee" stroke-width="2.5" stroke-linecap="round">
      <animate attributeName="stroke-dasharray" values="0 160;160 160" dur="1.2s" fill="freeze"/>
      <animateTransform attributeName="transform" 
                      type="rotate" 
                      from="0 28 28" 
                      to="360 28 28" 
                      dur="10s" 
                      repeatCount="indefinite"/>
    </path>
  </g>

  <circle cx="40" cy="40" r="38" stroke="url(#gradient)" stroke-width="2" opacity="0">
    <animate attributeName="opacity" values="0;0.6;0" dur="2s" begin="mouseover" fill="freeze"/>
  </circle>

  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#22d1ee;stop-opacity:0.5"/>
      <stop offset="100%" style="stop-color:#22d1ee;stop-opacity:0"/>
    </linearGradient>
  </defs>
</svg> 