import{g as e,K as s,s as a,e as o,f as t,h as i,w as r,i as c,o as n,j as l,m as d,t as u,x as h,v as f,y as C}from"./index-GgeQ7060.js";import{C as p}from"./custom-navbar.Dp8ceLKT.js";import{r as v}from"./uni-app.es.COHFzCZB.js";import{_ as m}from"./uni-icons.CMMZY0R_.js";import{u as _}from"./uqrcode.BN_KsmDm.js";import{_ as k}from"./_plugin-vue_export-helper.BCo6x5W8.js";const q=k({components:{CustomNavbar:p},data:()=>({shareCode:"",qrCodeLink:"",qrCodeUrl:""}),onShow(){this.getShareCode()},methods:{async getShareCode(){const s=e("userInfo");s&&(this.shareCode=s.shareCode||"",this.qrCodeLink=`https://invite.catcoinvip.com?invitecode=${this.shareCode}`,this.generateQrCode())},async generateQrCode(){try{const e=await _.generate(this.qrCodeLink);this.qrCodeUrl=e.tempFilePath}catch(e){console.error("生成二维码失败:",e)}},copyText(e){s({data:e,success:()=>{a({title:"已复制",icon:"success"})}})},shareToFriend(){this.copyText(this.qrCodeLink)},goInviteRecord(){o({url:"/pages/share/record"})}}},[["render",function(e,s,a,o,_,k){const q=v(t("custom-navbar"),p),y=h,g=c,x=f,b=C,j=v(t("uni-icons"),m);return n(),i(g,{class:"container"},{default:r((()=>[l(q,{title:"邀请好友",showBack:!0}),l(g,{class:"content"},{default:r((()=>[l(g,{class:"invite-card"},{default:r((()=>[l(g,{class:"invite-header"},{default:r((()=>[l(y,{class:"invite-title"}),l(y,{class:"invite-desc"},{default:r((()=>[d("探索区块链交易新机遇")])),_:1})])),_:1}),l(g,{class:"qrcode-box"},{default:r((()=>[_.qrCodeUrl?(n(),i(x,{key:0,src:_.qrCodeUrl,mode:"aspectFit",class:"qrcode-img"},null,8,["src"])):(n(),i(y,{key:1,class:"qrcode-loading"},{default:r((()=>[d("二维码生成中...")])),_:1}))])),_:1}),l(g,{class:"invite-row"},{default:r((()=>[l(y,{class:"invite-label"},{default:r((()=>[d("邀请码")])),_:1}),l(y,{class:"invite-value"},{default:r((()=>[d(u(_.shareCode),1)])),_:1}),l(g,{class:"copy-icon",onClick:s[0]||(s[0]=e=>k.copyText(_.shareCode))})])),_:1}),l(g,{class:"invite-row"},{default:r((()=>[l(y,{class:"invite-label"},{default:r((()=>[d("邀请链接")])),_:1}),l(y,{class:"invite-link"},{default:r((()=>[d(u(_.qrCodeLink),1)])),_:1}),l(g,{class:"copy-icon",onClick:s[1]||(s[1]=e=>k.copyText(_.qrCodeLink))})])),_:1}),l(b,{class:"invite-btn",onClick:k.shareToFriend},{default:r((()=>[d("马上邀请好友")])),_:1},8,["onClick"])])),_:1}),l(g,{class:"invite-record",onClick:k.goInviteRecord},{default:r((()=>[l(y,null,{default:r((()=>[d("邀请记录")])),_:1}),l(j,{type:"arrowright",size:"20",color:"#fff"})])),_:1},8,["onClick"])])),_:1})])),_:1})}],["__scopeId","data-v-de41340b"]]);export{q as default};
