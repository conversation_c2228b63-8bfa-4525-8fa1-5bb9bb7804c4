import{g as e,e as t,s as a,f as l,h as s,w as o,i,o as r,l as d,j as n,F as c,q as u,m as f,t as p,p as h,z as m,N as g,k as b,x as _,v as y,I as k,y as L}from"./index-GgeQ7060.js";import{_ as P}from"./uni-popup.DtPWGEwV.js";import{r as S}from"./uni-app.es.COHFzCZB.js";import{c as v}from"./index.B6QF5Ba_.js";import{r as T}from"./request.DgxtsFy5.js";import{_ as x}from"./_plugin-vue_export-helper.BCo6x5W8.js";const N=x({data:()=>({baseURL:v.apiBaseUrl,activeTab:"hold",activePeriod:60,tradeType:"up",mainPrice:"--",cnyPrice:"--",changeRate:"--",eventSource:null,sellList:[],buyList:[],profitList:[],profitPage:1,pageSize:10,profitTotal:0,loading:!1,profitHasMore:!0,holdList:[],holdPage:1,holdTotal:0,holdHasMore:!0,dealList:[],dealPage:1,dealTotal:0,dealHasMore:!0,trendList:[],currentPair:null,eventSourceTrend:null,currentTime:"",availableBalance:0,amount:"",orderLoading:!1,holdProfitSSE:null,orderbookLoading:!0,leverage:5,takeProfit:"",stopLoss:"",tabDataLoaded:{hold:!1,deal:!1,profit:!1},tabLoading:{hold:!1,deal:!1,profit:!1}}),mounted(){this.loadAllData(),this.setupSSE(),this.initTrendSSE(),this.loadUserInfo(),this.initHoldProfitSSE()},beforeDestroy(){this.holdProfitSSE&&(this.holdProfitSSE.close(),this.holdProfitSSE=null)},methods:{getImageUrl(e){return e?e.startsWith("/static/")?e:e.startsWith("/")?`${this.baseURL}${e}`:e:""},formatPrice(e){if(null==e||""===e||isNaN(e))return"--";let t=Number(e);if(0===t)return"0";let a=t.toString();return a.indexOf(".")>-1&&(a=a.replace(/(\.\d*?[1-9])0+$/,"$1").replace(/\.0+$/,"")),a},formatPrice2:e=>null==e||""===e||isNaN(e)?"--":Number(e).toFixed(2),formatAmount(e){if(null==e||""===e||isNaN(e))return"--";let t=Number(e);if(0===t)return"0";if(t>=1e9)return(t/1e9).toFixed(2)+"B";if(t>=1e6)return(t/1e6).toFixed(2)+"M";if(t>=1e3)return(t/1e3).toFixed(2)+"k";let a=t.toString();return a.length>6&&(a=t.toFixed(4)),a},formatTime(e){if(!e)return"";let t;if("string"==typeof e&&e.length>=19)t=new Date(e);else{if(!("number"==typeof e||"string"==typeof e&&/^\d+$/.test(e)))return"--";{let a=Number(e);if(10===e.length)t=new Date(1e3*a);else{if(13!==e.length)return"--";t=new Date(a)}}}const a=e=>e<10?"0"+e:e;return`${a(t.getHours())}:${a(t.getMinutes())}:${a(t.getSeconds())}`},formatPairPrice(e){if(null==e||""===e||isNaN(e))return"--";const t=Number(e);return t>=1?t.toFixed(2):t.toFixed(5).replace(/0+$/,"").replace(/\.$/,"")},setupSSE(){this.eventSource&&this.eventSource.close();const t=e("token")||"";t&&(this.eventSource=new window.EventSource(this.baseURL+"/api/market/futures/stream?token="+encodeURIComponent(t)+"&symbol="+encodeURIComponent(this.currentPair)),this.eventSource.onmessage=e=>{const t=JSON.parse(e.data);"orderbook"!==t.type&&"futures_update"!==t.type||(this.buyList=t.buyList||[],this.sellList=t.sellList||[],this.orderbookLoading=!1),"ticker"!==t.type&&"futures_update"!==t.type||(this.mainPrice=t.price||"--",this.cnyPrice=t.cnyPrice||"--",this.changeRate=t.changeRate||"--")},this.eventSource.onerror=()=>{this.eventSource.close(),setTimeout((()=>this.setupSSE()),5e3)})},initTrendSSE(){this.eventSourceTrend&&this.eventSourceTrend.close();const t=e("token")||"";t&&(this.eventSourceTrend=new window.EventSource(this.baseURL+"/api/market/trend/stream?token="+encodeURIComponent(t)),this.eventSourceTrend.onmessage=e=>{let t=e.data;t.startsWith("data: ")&&(t=t.slice(6));try{const e=JSON.parse(t);e.forEach((e=>{e.cnyPrice=(7*Number(e.price)).toFixed(2)})),this.trendList=e,!this.currentPair&&e.length>0&&(this.currentPair=e[0],this.switchPair(e[0]))}catch(a){}},this.eventSourceTrend.onerror=()=>{this.eventSourceTrend.close()})},selectPair(e){this.currentPair=e,this.$refs.pairPopup&&this.$refs.pairPopup.close&&this.$refs.pairPopup.close(),this.switchPair(e)},switchPair(t){this.eventSource&&this.eventSource.close();const a=e("token")||"";a&&(this.eventSource=new window.EventSource(this.baseURL+"/api/market/futures/stream?symbol="+encodeURIComponent(t.pairName)+"&token="+encodeURIComponent(a)),this.eventSource.onmessage=e=>{const t=JSON.parse(e.data);"orderbook"!==t.type&&"futures_update"!==t.type||(this.buyList=t.buyList||[],this.sellList=t.sellList||[],this.orderbookLoading=!1),"ticker"!==t.type&&"futures_update"!==t.type||(this.mainPrice=t.price||"--",this.cnyPrice=t.cnyPrice||"--",this.changeRate=t.changeRate||"--")},this.eventSource.onerror=()=>{this.eventSource.close(),setTimeout((()=>this.switchPair(t)),5e3)})},goKline(){const e=this.currentPair?this.currentPair.pairName:"BTCUSDT";t({url:`/pages/kline/index?symbol=${encodeURIComponent(e)}&from=futures`})},openPairPopup(){this.$refs.pairPopup&&this.$refs.pairPopup.open()},async loadProfitList(e=!1){this.tabLoading.profit=!0;try{const t=await T({url:"/api/futures/option/profit",method:"GET",params:{page:this.profitPage,size:this.pageSize}});if(200===t.code){const a=t.data.records||[];this.profitList=e?this.profitList.concat(a):a,this.profitTotal=t.data.total,this.profitHasMore=this.profitList.length<this.profitTotal}}finally{this.tabLoading.profit=!1}},async loadHoldList(e=!1){this.tabLoading.hold=!0;try{const t=await T({url:"/api/futures/option/hold",method:"GET",params:{page:this.holdPage,size:this.pageSize}});if(200===t.code){const a=t.data.records||[];this.holdList=e?this.holdList.concat(a):a,this.holdTotal=t.data.total,this.holdHasMore=this.holdList.length<this.holdTotal}}finally{this.tabLoading.hold=!1}},async loadDealList(e=!1){this.tabLoading.deal=!0;try{const t=await T({url:"/api/futures/option/deal",method:"GET",params:{page:this.dealPage,size:this.pageSize}});if(200===t.code){const a=t.data.records||[];this.dealList=e?this.dealList.concat(a):a,this.dealTotal=t.data.total,this.dealHasMore=this.dealList.length<this.dealTotal}}finally{this.tabLoading.deal=!1}},async loadUserInfo(){try{const e=await T({url:"/api/user/info",method:"GET"});200===e.code&&e.data&&(this.availableBalance=e.data.availableBalance)}catch(e){}},async onOrderConfirm(){a({title:"暂未开放",icon:"none",duration:2e3})},onProfitLoadMore(){!this.tabLoading.profit&&this.profitHasMore&&(this.profitPage++,this.loadProfitList(!0))},onHoldLoadMore(){!this.tabLoading.hold&&this.holdHasMore&&(this.holdPage++,this.loadHoldList(!0))},onDealLoadMore(){!this.tabLoading.deal&&this.dealHasMore&&(this.dealPage++,this.loadDealList(!0))},onTabChange(e){this.activeTab=e,this.tabDataLoaded[e]||("profit"===e?(this.profitPage=1,this.loadProfitList(!1),this.tabDataLoaded.profit=!0):"hold"===e?(this.holdPage=1,this.loadHoldList(!1),this.tabDataLoaded.hold=!0):"deal"===e&&(this.dealPage=1,this.loadDealList(!1),this.tabDataLoaded.deal=!0))},initHoldProfitSSE(){this.holdProfitSSE&&this.holdProfitSSE.close();const t=e("token")||"";this.holdProfitSSE=new window.EventSource(this.baseURL+"/api/futures/option/hold/profit/stream?token="+encodeURIComponent(t)),this.holdProfitSSE.onmessage=async e=>{let t=e.data;t.startsWith("data: ")&&(t=t.slice(6));try{const e=JSON.parse(t);let a=!1;e.forEach((e=>{const t=this.holdList.findIndex((t=>t.id===e.orderId));-1!==t&&(1===e.status?(this.holdList.splice(t,1),a=!0):(this.holdList[t].profit=e.profit,this.holdList[t].percent=e.percent))})),a&&await this.loadUserInfo()}catch(a){}},this.holdProfitSSE.onerror=()=>{this.holdProfitSSE.close(),setTimeout((()=>this.initHoldProfitSSE()),5e3)}},async loadAllData(){this.loading=!0,"hold"!==this.activeTab||this.tabDataLoaded.hold||(await this.loadHoldList(!1),this.tabDataLoaded.hold=!0),this.loading=!1},onNumberInput(e,t){let a=t.detail.value;a=a.replace(/[^\d.]/g,"").replace(/\.(?=.*\.)/g,""),a.startsWith("00")&&(a=a.replace(/^0+/,"0")),a.indexOf(".")>-1&&(a=a.replace(/(\.\d{0,6}).*$/,"$1")),this[e]=a},refreshCurrentTab(){const e=this.activeTab;"profit"===e?(this.profitPage=1,this.loadProfitList(!1)):"hold"===e?(this.holdPage=1,this.loadHoldList(!1)):"deal"===e&&(this.dealPage=1,this.loadDealList(!1))}}},[["render",function(e,t,a,v,T,x){const N=i,w=_,C=y,F=k,D=L,U=S(l("uni-popup"),P);return r(),s(N,{class:"futures-page"},{default:o((()=>[T.loading?(r(),d(c,{key:0},[n(N,{class:"skeleton-header"}),n(N,{class:"skeleton-main"},{default:o((()=>[n(N,{class:"skeleton-orderbook"}),n(N,{class:"skeleton-trade-panel"},{default:o((()=>[n(N,{class:"skeleton-timer"}),n(N,{class:"skeleton-main-price"}),n(N,{class:"skeleton-btns"}),n(N,{class:"skeleton-input"}),n(N,{class:"skeleton-period-list"}),n(N,{class:"skeleton-confirm"})])),_:1})])),_:1}),n(N,{class:"skeleton-tabs"},{default:o((()=>[(r(),d(c,null,u(3,(e=>n(N,{class:"skeleton-tab",key:e},{default:o((()=>[n(N,{class:"skeleton-tab-header"}),(r(),d(c,null,u(3,(e=>n(N,{class:"skeleton-tab-row",key:e}))),64))])),_:2},1024))),64))])),_:1})],64)):(r(),d(c,{key:1},[n(N,{class:"futures-header"},{default:o((()=>[n(N,{style:{display:"flex","align-items":"center",flex:"1"}},{default:o((()=>[n(N,{class:"pair-select",onClick:x.openPairPopup},{default:o((()=>[n(w,{class:"pair"},{default:o((()=>[f(p(T.currentPair?T.currentPair.pairName.replace("USDT",""):"BTC"),1)])),_:1}),n(w,{style:{color:"#fff","font-size":"12px","margin-left":"6rpx","vertical-align":"middle"}},{default:o((()=>[f("▼")])),_:1})])),_:1},8,["onClick"]),n(N,{class:"trend-rate",style:{"margin-left":"12rpx"}},{default:o((()=>[n(w,{class:h(["rate",Number(T.changeRate)<0?"down":"up"])},{default:o((()=>[f(p(Number(T.changeRate)>0&&!String(T.changeRate).startsWith("+")&&!String(T.changeRate).startsWith("-")?"+":"")+p(x.formatPrice(T.changeRate))+"% ",1)])),_:1},8,["class"])])),_:1})])),_:1}),n(N,{class:"header-btns"},{default:o((()=>[n(N,{class:"header-btn",onClick:x.goKline},{default:o((()=>[n(C,{src:"/assets/kline-CNhJb2ex.png",style:{width:"100px",height:"100px"},mode:"aspectFit"})])),_:1},8,["onClick"])])),_:1})])),_:1}),n(N,{class:"futures-main"},{default:o((()=>[n(N,{class:"orderbook"},{default:o((()=>[T.orderbookLoading?(r(),d(c,{key:0},[n(N,{class:"orderbook-header"},{default:o((()=>[n(w,null,{default:o((()=>[f("价格(USDT)")])),_:1}),n(w,null,{default:o((()=>[f("数量("+p(T.currentPair?T.currentPair.pairName.replace("USDT",""):"BTC")+")",1)])),_:1})])),_:1}),n(N,{class:"orderbook-list"},{default:o((()=>[(r(),d(c,null,u(6,(e=>n(N,{class:"orderbook-row skeleton-row",key:"sell-skeleton"+e}))),64)),n(N,{class:"orderbook-divider"}),(r(),d(c,null,u(6,(e=>n(N,{class:"orderbook-row skeleton-row",key:"buy-skeleton"+e}))),64))])),_:1})],64)):(r(),d(c,{key:1},[n(N,{class:"orderbook-header"},{default:o((()=>[n(w,null,{default:o((()=>[f("价格(USDT)")])),_:1}),n(w,null,{default:o((()=>[f("数量("+p(T.currentPair?T.currentPair.pairName.replace("USDT",""):"BTC")+")",1)])),_:1})])),_:1}),n(N,{class:"orderbook-list"},{default:o((()=>[(r(!0),d(c,null,u(T.sellList.slice(0,6).reverse(),((e,t)=>(r(),s(N,{class:"orderbook-row red",key:"sell"+t},{default:o((()=>[n(w,null,{default:o((()=>[f(p(x.formatPrice2(e.price)),1)])),_:2},1024),n(w,null,{default:o((()=>[f(p(x.formatAmount(e.amount)),1)])),_:2},1024)])),_:2},1024)))),128)),n(N,{class:"orderbook-divider"}),(r(!0),d(c,null,u(T.buyList.slice(0,6),((e,t)=>(r(),s(N,{class:"orderbook-row green",key:"buy"+t},{default:o((()=>[n(w,null,{default:o((()=>[f(p(x.formatPrice2(e.price)),1)])),_:2},1024),n(w,null,{default:o((()=>[f(p(x.formatAmount(e.amount)),1)])),_:2},1024)])),_:2},1024)))),128))])),_:1})],64))])),_:1}),n(N,{class:"trade-panel"},{default:o((()=>[n(N,{class:"trade-price"},{default:o((()=>[n(w,{class:"main-price",style:m({color:Number(T.changeRate)>0?"#FF4D4F":Number(T.changeRate)<0?"#00FF99":"#fff"})},{default:o((()=>[f(p(x.formatPrice(T.mainPrice)),1)])),_:1},8,["style"]),n(w,{class:"cny",style:m({color:Number(T.changeRate)>0?"#FF4D4F":Number(T.changeRate)<0?"#00FF99":"#FFE066"})},{default:o((()=>[f(" ≈"+p(x.formatPrice(T.cnyPrice))+"CNY ",1)])),_:1},8,["style"])])),_:1}),n(N,{class:"trade-btns"},{default:o((()=>[n(N,{class:h(["trade-type","up"===T.tradeType?"active":""]),onClick:t[0]||(t[0]=e=>T.tradeType="up")},{default:o((()=>[f("买涨")])),_:1},8,["class"]),n(N,{class:h(["trade-type","down"===T.tradeType?"active":""]),onClick:t[1]||(t[1]=e=>T.tradeType="down")},{default:o((()=>[f("买跌")])),_:1},8,["class"])])),_:1}),n(N,{class:"trade-amount"},{default:o((()=>[n(N,{class:"leverage-switch-group trade-btns",style:{"margin-bottom":"18rpx"}},{default:o((()=>[n(N,{class:h(["trade-type",5===T.leverage?"active":""]),onClick:t[2]||(t[2]=e=>T.leverage=5)},{default:o((()=>[f("X5")])),_:1},8,["class"]),n(N,{class:h(["trade-type",10===T.leverage?"active":""]),onClick:t[3]||(t[3]=e=>T.leverage=10)},{default:o((()=>[f("X10")])),_:1},8,["class"])])),_:1}),n(N,{class:"trade-info"},{default:o((()=>[n(w,null,{default:o((()=>[f("可用 "),g("span",{style:{color:"#fff"}},p(T.availableBalance)+"USDT",1)])),_:1})])),_:1}),n(N,{style:{position:"relative",display:"flex","align-items":"center"}},{default:o((()=>[n(F,{type:"number",class:"amount-input with-suffix",value:T.amount,placeholder:"数量",style:{flex:"1","padding-right":"60rpx"},onInput:t[4]||(t[4]=e=>x.onNumberInput("amount",e)),maxlength:"10"},null,8,["value"]),g("span",{class:"input-suffix"},p(T.currentPair?T.currentPair.pairName.replace(/(USDT|BTC|ETH|BNB|USD|U)$/i,""):"BTC"),1)])),_:1}),n(F,{type:"number",class:"amount-input",value:T.takeProfit,placeholder:"止盈价USDT",style:{"margin-top":"12rpx"},onInput:t[5]||(t[5]=e=>x.onNumberInput("takeProfit",e)),maxlength:"10"},null,8,["value"]),n(F,{type:"number",class:"amount-input",value:T.stopLoss,placeholder:"止损价USDT",style:{"margin-top":"12rpx"},onInput:t[6]||(t[6]=e=>x.onNumberInput("stopLoss",e)),maxlength:"10"},null,8,["value"]),n(N,{style:{display:"flex","justify-content":"space-between","align-items":"center","margin-top":"16rpx"}},{default:o((()=>[n(w,{style:{color:"#fff","font-size":"18rpx"}},{default:o((()=>[f("保证金")])),_:1}),n(w,{style:{color:"#fff","font-size":"18rpx","font-weight":"bold"}},{default:o((()=>[f(p(T.amount&&!isNaN(T.amount)&&T.leverage?(Number(T.amount)/Number(T.leverage)).toFixed(4):"--"),1)])),_:1})])),_:1})])),_:1}),n(D,{class:"trade-confirm",onClick:x.onOrderConfirm,disabled:T.orderLoading},{default:o((()=>[T.orderLoading?(r(),s(w,{key:0},{default:o((()=>[f("下单中...")])),_:1})):(r(),s(w,{key:1},{default:o((()=>[f("确定")])),_:1}))])),_:1},8,["onClick","disabled"])])),_:1})])),_:1}),n(N,{class:"futures-tabs"},{default:o((()=>[n(N,{class:"tab-list"},{default:o((()=>[n(N,{class:h(["tab","hold"===T.activeTab?"active":""]),onClick:t[7]||(t[7]=e=>x.onTabChange("hold"))},{default:o((()=>[f("持仓")])),_:1},8,["class"]),n(N,{class:h(["tab","deal"===T.activeTab?"active":""]),onClick:t[8]||(t[8]=e=>x.onTabChange("deal"))},{default:o((()=>[f("成交")])),_:1},8,["class"]),n(N,{class:h(["tab","profit"===T.activeTab?"active":""]),onClick:t[9]||(t[9]=e=>x.onTabChange("profit"))},{default:o((()=>[f("盈利")])),_:1},8,["class"])])),_:1}),"hold"===T.activeTab?(r(),s(N,{key:0,class:"hold-card-list",style:{"min-height":"700rpx"}},{default:o((()=>[T.tabLoading.hold&&0===T.holdList.length?(r(),s(N,{key:0,class:"tab-loading"},{default:o((()=>[n(w,{class:"loading-text"},{default:o((()=>[f("加载中...")])),_:1})])),_:1})):b("",!0),0!==T.holdList.length||T.tabLoading.hold?b("",!0):(r(),s(N,{key:1,class:"empty-state"},{default:o((()=>[n(w,{class:"empty-text"},{default:o((()=>[f("暂无持仓数据")])),_:1})])),_:1})),(r(!0),d(c,null,u(T.holdList,((e,t)=>(r(),s(N,{key:e.id,class:"hold-card"},{default:o((()=>[n(N,{class:"hold-row-2col"},{default:o((()=>[n(N,{class:"hold-col"},{default:o((()=>[n(w,{class:"hold-label"},{default:o((()=>[f("时间")])),_:1}),n(w,{class:"hold-value"},{default:o((()=>[f(p(x.formatTime(e.orderTime)),1)])),_:2},1024)])),_:2},1024),n(N,{class:"hold-col"},{default:o((()=>[n(w,{class:"hold-label"},{default:o((()=>[f("方向")])),_:1}),n(w,{class:"hold-value",style:m("up"===e.direction?"color: #FF4D4F":"color: #00FF99")},{default:o((()=>[f(p("up"===e.direction?"买涨":"买跌"),1)])),_:2},1032,["style"])])),_:2},1024)])),_:2},1024),n(N,{class:"hold-row-2col"},{default:o((()=>[n(N,{class:"hold-col"},{default:o((()=>[n(w,{class:"hold-label"},{default:o((()=>[f("金额")])),_:1}),n(w,{class:"hold-value"},{default:o((()=>[f(p(e.amount),1)])),_:2},1024)])),_:2},1024),n(N,{class:"hold-col"},{default:o((()=>[n(w,{class:"hold-label"},{default:o((()=>[f("开仓价")])),_:1}),n(w,{class:"hold-value"},{default:o((()=>[f(p(e.openPrice),1)])),_:2},1024)])),_:2},1024)])),_:2},1024),n(N,{class:"hold-row-2col"},{default:o((()=>[n(N,{class:"hold-col"},{default:o((()=>[n(w,{class:"hold-label"},{default:o((()=>[f("盈利")])),_:1}),n(w,{class:h(["hold-value",e.profit>0?"red":"green"])},{default:o((()=>[f(p(void 0!==e.profit?e.profit:"--"),1)])),_:2},1032,["class"])])),_:2},1024),n(N,{class:"hold-col"},{default:o((()=>[n(w,{class:"hold-label"},{default:o((()=>[f("收益率")])),_:1}),n(w,{class:h(["hold-value",e.percent>0?"red":"green"])},{default:o((()=>[f(p(void 0!==e.percent?(100*e.percent).toFixed(2)+"%":"--"),1)])),_:2},1032,["class"])])),_:2},1024)])),_:2},1024),n(N,{class:"hold-row-2col"},{default:o((()=>[n(N,{class:"hold-col"},{default:o((()=>[n(w,{class:"hold-label"},{default:o((()=>[f("状态")])),_:1}),n(w,{class:"hold-value"},{default:o((()=>[f(p(0===e.status?"待成交":1===e.status?"已成交":"--"),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128))])),_:1})):b("",!0),"deal"===T.activeTab?(r(),s(N,{key:1,class:"tab-table",style:{"min-height":"700rpx"}},{default:o((()=>[n(N,{class:"table-header"},{default:o((()=>[n(w,null,{default:o((()=>[f("时间")])),_:1}),n(w,null,{default:o((()=>[f("方向")])),_:1}),n(w,null,{default:o((()=>[f("盈亏")])),_:1}),n(w,null,{default:o((()=>[f("平仓价")])),_:1})])),_:1}),T.tabLoading.deal&&0===T.dealList.length?(r(),s(N,{key:0,class:"tab-loading"},{default:o((()=>[n(w,{class:"loading-text"},{default:o((()=>[f("加载中...")])),_:1})])),_:1})):b("",!0),0!==T.dealList.length||T.tabLoading.deal?b("",!0):(r(),s(N,{key:1,class:"empty-state"},{default:o((()=>[n(w,{class:"empty-text"},{default:o((()=>[f("暂无成交数据")])),_:1})])),_:1})),(r(!0),d(c,null,u(T.dealList,((e,t)=>(r(),s(N,{class:"table-row",key:e.id},{default:o((()=>[n(w,null,{default:o((()=>[f(p(x.formatTime(e.settleTime)),1)])),_:2},1024),n(w,{style:m("up"===e.direction?"color: #FF6A00":"color: #00FF99")},{default:o((()=>[f(p("up"===e.direction?"买涨":"买跌"),1)])),_:2},1032,["style"]),n(w,{class:h(e.profit>0?"red":"green")},{default:o((()=>[f(p(e.profit),1)])),_:2},1032,["class"]),n(w,{class:h(e.profit>0?"red":"green")},{default:o((()=>[f(p(e.closePrice),1)])),_:2},1032,["class"])])),_:2},1024)))),128)),T.dealHasMore?(r(),s(N,{key:2,class:"table-more"},{default:o((()=>[T.dealHasMore?(r(),s(D,{key:0,onClick:x.onDealLoadMore,disabled:T.tabLoading.deal},{default:o((()=>[f("加载更多")])),_:1},8,["onClick","disabled"])):(r(),s(w,{key:1,style:{color:"#fff","font-size":"22rpx"}},{default:o((()=>[f("已经没有了")])),_:1}))])),_:1})):b("",!0)])),_:1})):b("",!0),"profit"===T.activeTab?(r(),s(N,{key:2,class:"tab-table",style:{"min-height":"700rpx"}},{default:o((()=>[n(N,{class:"table-header"},{default:o((()=>[n(w,null,{default:o((()=>[f("时间")])),_:1}),n(w,null,{default:o((()=>[f("方向")])),_:1}),n(w,null,{default:o((()=>[f("获利")])),_:1}),n(w,null,{default:o((()=>[f("平仓价")])),_:1})])),_:1}),T.tabLoading.profit&&0===T.profitList.length?(r(),s(N,{key:0,class:"tab-loading"},{default:o((()=>[n(w,{class:"loading-text"},{default:o((()=>[f("加载中...")])),_:1})])),_:1})):b("",!0),0!==T.profitList.length||T.tabLoading.profit?b("",!0):(r(),s(N,{key:1,class:"empty-state"},{default:o((()=>[n(w,{class:"empty-text"},{default:o((()=>[f("暂无盈利数据")])),_:1})])),_:1})),(r(!0),d(c,null,u(T.profitList,((e,t)=>(r(),s(N,{class:"table-row",key:e.id},{default:o((()=>[n(w,null,{default:o((()=>[f(p(x.formatTime(e.settleTime)),1)])),_:2},1024),n(w,{style:m("up"===e.direction?"color: #FF6A00":"color: #00FF99")},{default:o((()=>[f(p("up"===e.direction?"买涨":"买跌"),1)])),_:2},1032,["style"]),n(w,{class:h(e.profit>0?"red":"green")},{default:o((()=>[f(p(e.profit),1)])),_:2},1032,["class"]),n(w,null,{default:o((()=>[f(p(e.closePrice),1)])),_:2},1024)])),_:2},1024)))),128)),T.profitHasMore?(r(),s(N,{key:2,class:"table-more"},{default:o((()=>[T.profitHasMore?(r(),s(D,{key:0,onClick:x.onProfitLoadMore,disabled:T.tabLoading.profit},{default:o((()=>[f("加载更多")])),_:1},8,["onClick","disabled"])):(r(),s(w,{key:1,style:{color:"#fff","font-size":"22rpx"}},{default:o((()=>[f("已经没有了")])),_:1}))])),_:1})):b("",!0)])),_:1})):b("",!0)])),_:1}),n(U,{ref:"pairPopup",type:"bottom","mask-click":!0},{default:o((()=>[n(N,{class:"pair-popup"},{default:o((()=>[n(N,{class:"pair-popup-title"},{default:o((()=>[f("选择币对")])),_:1}),n(N,{class:"pair-popup-list"},{default:o((()=>[(r(!0),d(c,null,u(T.trendList,(e=>(r(),s(N,{key:e.pairName,class:h(["pair-popup-item",{active:T.currentPair&&T.currentPair.pairName===e.pairName}]),onClick:t=>x.selectPair(e)},{default:o((()=>[n(C,{src:x.getImageUrl(e.logoUrl),class:"pair-popup-logo",mode:"aspectFit"},null,8,["src"]),n(N,{class:"pair-popup-info"},{default:o((()=>[n(w,{class:"pair-popup-name"},{default:o((()=>[f(p(e.pairName.replace("USDT","")),1)])),_:2},1024),n(w,{class:"pair-popup-token"},{default:o((()=>[f(p(e.tokenName),1)])),_:2},1024)])),_:2},1024),n(N,{class:"pair-popup-price"},{default:o((()=>[n(w,{class:"pair-price"},{default:o((()=>[f(p(x.formatPairPrice(e.price)),1)])),_:2},1024),n(w,{class:h(["pair-change",Number(e.change)>0?"rise":"fall"])},{default:o((()=>[f(p(Number(e.change)>0?"+":"-")+p(Math.abs(Number(e.change)).toFixed(2))+"% ",1)])),_:2},1032,["class"])])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1})])),_:1},512)],64))])),_:1})}],["__scopeId","data-v-9334cfb7"]]);export{N as default};
