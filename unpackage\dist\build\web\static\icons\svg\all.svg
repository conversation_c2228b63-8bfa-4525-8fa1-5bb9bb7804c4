<svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景圆圈 -->
  <circle cx="40" cy="40" r="40" fill="rgba(34, 209, 238, 0.1)">
    <animate attributeName="opacity" values="0.1;0.15;0.1" dur="2s" repeatCount="indefinite"/>
  </circle>
  
  <!-- 主图标组 -->
  <g transform="translate(12, 12)">
    <!-- 外圈 -->
    <circle cx="28" cy="28" r="24" stroke="#22d1ee" stroke-width="2.5" opacity="0.2"/>
    
    <!-- 内部填充效果 -->
    <rect x="16" y="16" width="24" height="24" fill="#22d1ee" opacity="0.1" rx="4">
      <animate attributeName="opacity" 
               values="0.1;0.2;0.1" 
               dur="2s" 
               repeatCount="indefinite"/>
    </rect>
    
    <!-- 三条动态线 -->
    <path d="M16 22h24" stroke="#22d1ee" stroke-width="2.5" stroke-linecap="round">
      <animate attributeName="stroke-dasharray" 
               values="0 24;24 24" 
               dur="0.8s" 
               fill="freeze"/>
      <animate attributeName="opacity" 
               values="0.6;1;0.6" 
               dur="3s" 
               repeatCount="indefinite"/>
    </path>
    <path d="M16 28h24" stroke="#22d1ee" stroke-width="2.5" stroke-linecap="round">
      <animate attributeName="stroke-dasharray" 
               values="0 24;24 24" 
               dur="0.8s" 
               begin="0.2s" 
               fill="freeze"/>
      <animate attributeName="opacity" 
               values="0.6;1;0.6" 
               dur="3s" 
               begin="0.2s"
               repeatCount="indefinite"/>
    </path>
    <path d="M16 34h24" stroke="#22d1ee" stroke-width="2.5" stroke-linecap="round">
      <animate attributeName="stroke-dasharray" 
               values="0 24;24 24" 
               dur="0.8s" 
               begin="0.4s" 
               fill="freeze"/>
      <animate attributeName="opacity" 
               values="0.6;1;0.6" 
               dur="3s" 
               begin="0.4s"
               repeatCount="indefinite"/>
    </path>
  </g>

  <!-- 悬停效果 -->
  <circle cx="40" cy="40" r="38" stroke="url(#gradient)" stroke-width="2" opacity="0">
    <animate attributeName="opacity" 
             values="0;0.6;0" 
             dur="2s" 
             begin="mouseover" 
             fill="freeze"/>
  </circle>

  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#22d1ee;stop-opacity:0.5"/>
      <stop offset="100%" style="stop-color:#22d1ee;stop-opacity:0"/>
    </linearGradient>
  </defs>
</svg> 