import{g as a,a3 as e,a as s,s as t,A as o}from"./index-GgeQ7060.js";import{c as r}from"./index.B6QF5Ba_.js";const n=(n={})=>new Promise(((d,i)=>{const m=a("token");let p=r.apiBaseUrl+n.url;if(n.params){p+=`?${Object.keys(n.params).map((a=>`${a}=${encodeURIComponent(n.params[a])}`)).join("&")}`}e({url:p,method:n.method||"GET",data:n.data||{},header:{"content-type":"application/json",...m?{Authorization:`Bearer ${m}`}:{},...n.header},success:a=>{if(401===a.statusCode)return s("token"),s("userInfo"),t({title:"登录已过期，请重新登录",icon:"none"}),setTimeout((()=>{o({url:"/pages/login/index"})}),1500),void i(new Error("请先登录"));200===a.statusCode?200===a.data.code?d(a.data):i({message:a.data.message||"操作失败",...a.data}):i({message:a.data.message||"请求失败",...a.data})},fail:a=>{i({message:"网络请求失败",...a})}})}));export{n as r};
