import{n as e,s as t,A as o,f as a,h as i,w as s,i as n,o as l,j as c,m as r,t as d,p as u,k as f,x as m,I as p,y as h}from"./index-GgeQ7060.js";import{_ as k}from"./uni-icons.CMMZY0R_.js";import{r as w}from"./uni-app.es.COHFzCZB.js";import{r as y}from"./request.DgxtsFy5.js";import{t as b}from"./i18n.D3rYKUqN.js";import{_ as C}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.B6QF5Ba_.js";const g=C({data:()=>({showPhoneReset:!1,resetType:"email",fakeForm:{a:"",b:"",c:"",d:""},isCountingDown:!1,countdown:60,timer:null,isSubmitting:!1,isCodeSending:!1}),computed:{countDownText(){return this.isCountingDown?`${this.countdown}s`:b("getCode")}},methods:{t:b,goBack(){e()},async sendCode(){if(!this.isCountingDown&&!this.isCodeSending){this.isCodeSending=!0;try{if("phone"===this.resetType){if(!this.fakeForm.a)return void t({title:b("inputPhone"),icon:"none"});await y({url:"/api/auth/send-reset-code-phone",method:"POST",params:{phone:this.fakeForm.a}})}else{if(!this.fakeForm.a)return void t({title:b("inputEmail"),icon:"none"});await y({url:"/api/auth/send-reset-code-email",method:"POST",params:{email:this.fakeForm.a}})}t({title:b("codeSent"),icon:"success"}),this.startCountDown()}catch(e){console.log(e),t({title:b("sendFail"),icon:"none"})}finally{this.isCodeSending=!1}}},startCountDown(){this.isCountingDown=!0,this.countdown=60,this.timer=setInterval((()=>{this.countdown>0?this.countdown--:this.stopCountDown()}),1e3)},stopCountDown(){clearInterval(this.timer),this.timer=null,this.isCountingDown=!1,this.countdown=60},async handleReset(){if(!this.isSubmitting){if("phone"===this.resetType){if(!this.fakeForm.a)return void t({title:b("inputPhone"),icon:"none"})}else if(!this.fakeForm.a)return void t({title:b("inputEmail"),icon:"none"});if(this.fakeForm.b)if(this.fakeForm.c)if(this.fakeForm.c.length<6)t({title:b("pwdMinLen"),icon:"none"});else if(this.fakeForm.c===this.fakeForm.d){this.isSubmitting=!0;try{await y({url:"/api/auth/reset-password",method:"POST",data:{phone:"phone"===this.resetType?this.fakeForm.a:void 0,email:"email"===this.resetType?this.fakeForm.a:void 0,code:this.fakeForm.b,newPassword:this.fakeForm.c}}),t({title:b("resetSuccess"),icon:"success"}),setTimeout((()=>{o({url:"/pages/login/index"})}),1e3)}catch(e){t({title:e.message||b("resetFail"),icon:"none"})}finally{this.isSubmitting=!1}}else t({title:b("pwdNotMatch"),icon:"none"});else t({title:b("inputNewPwd"),icon:"none"});else t({title:b("inputCode"),icon:"none"})}},clearForm(){this.fakeForm={a:"",b:"",c:"",d:""}}},beforeDestroy(){this.timer&&clearInterval(this.timer)}},[["render",function(e,t,o,y,b,C){const g=w(a("uni-icons"),k),F=n,_=m,T=p,S=h;return l(),i(F,{class:"reset-container"},{default:s((()=>[c(F,{class:"custom-navbar"},{default:s((()=>[c(F,{class:"navbar-content"},{default:s((()=>[c(F,{class:"left-area",onClick:C.goBack},{default:s((()=>[c(g,{type:"left",size:"20",color:"#fff"})])),_:1},8,["onClick"])])),_:1})])),_:1}),c(F,{class:"reset-welcome"},{default:s((()=>[c(_,{class:"welcome-title"},{default:s((()=>[r(d(C.t("resetTitle")),1)])),_:1}),c(_,{class:"welcome-sub"},{default:s((()=>[r(d(b.showPhoneReset?C.t("resetSubtitle"):C.t("resetSubtitleEmail")),1)])),_:1})])),_:1}),b.showPhoneReset?(l(),i(F,{key:0,class:"reset-type-tabs"},{default:s((()=>[c(F,{class:u(["tab","phone"===b.resetType?"active":""]),onClick:t[0]||(t[0]=e=>b.resetType="phone")},{default:s((()=>[r(d(C.t("phoneTab")),1)])),_:1},8,["class"]),c(F,{class:u(["tab","email"===b.resetType?"active":""]),onClick:t[1]||(t[1]=e=>b.resetType="email")},{default:s((()=>[r(d(C.t("emailTab")),1)])),_:1},8,["class"])])),_:1})):f("",!0),c(F,{class:u(["form-box",{"full-radius":!b.showPhoneReset}])},{default:s((()=>[b.showPhoneReset&&"phone"===b.resetType?(l(),i(F,{key:0,class:"input-item"},{default:s((()=>[c(g,{type:"phone",size:"20",color:"#ffd700"}),c(T,{modelValue:b.fakeForm.a,"onUpdate:modelValue":t[2]||(t[2]=e=>b.fakeForm.a=e),type:"text",name:"not-phone",style:{background:"none !important"},placeholder:C.t("inputPhone"),autocomplete:"off",autocapitalize:"off",autocorrect:"off",spellcheck:"false"},null,8,["modelValue","placeholder"])])),_:1})):f("",!0),b.showPhoneReset&&"email"!==b.resetType?f("",!0):(l(),i(F,{key:1,class:"input-item"},{default:s((()=>[c(g,{type:"email",size:"20",color:"#ffd700"}),c(T,{modelValue:b.fakeForm.a,"onUpdate:modelValue":t[3]||(t[3]=e=>b.fakeForm.a=e),type:"text",name:"not-email",placeholder:C.t("inputEmail"),autocomplete:"off",autocapitalize:"off",autocorrect:"off",spellcheck:"false"},null,8,["modelValue","placeholder"])])),_:1})),c(F,{class:"input-item code-item"},{default:s((()=>[c(g,{type:"auth",size:"20",color:"#ffd700"}),c(T,{modelValue:b.fakeForm.b,"onUpdate:modelValue":t[4]||(t[4]=e=>b.fakeForm.b=e),type:"text",name:"not-code",placeholder:C.t("inputCode"),autocomplete:"off",autocapitalize:"off",autocorrect:"off",spellcheck:"false"},null,8,["modelValue","placeholder"]),c(S,{class:"code-btn",onClick:C.sendCode,disabled:b.isCountingDown||b.isCodeSending},{default:s((()=>[r(d(b.isCodeSending?C.t("sending"):C.countDownText),1)])),_:1},8,["onClick","disabled"])])),_:1}),c(F,{class:"input-item"},{default:s((()=>[c(g,{type:"locked",size:"20",color:"#ffd700"}),c(T,{type:"password",modelValue:b.fakeForm.c,"onUpdate:modelValue":t[5]||(t[5]=e=>b.fakeForm.c=e),name:"not-newpwd",placeholder:C.t("inputNewPwd"),autocomplete:"off",autocapitalize:"off",autocorrect:"off",spellcheck:"false"},null,8,["modelValue","placeholder"])])),_:1}),c(F,{class:"input-item"},{default:s((()=>[c(g,{type:"locked",size:"20",color:"#ffd700"}),c(T,{type:"password",modelValue:b.fakeForm.d,"onUpdate:modelValue":t[6]||(t[6]=e=>b.fakeForm.d=e),name:"not-confirm",placeholder:C.t("inputConfirmPwd"),autocomplete:"off",autocapitalize:"off",autocorrect:"off",spellcheck:"false"},null,8,["modelValue","placeholder"])])),_:1}),c(S,{class:"submit-btn",onClick:C.handleReset,disabled:b.isSubmitting},{default:s((()=>[r(d(b.isSubmitting?C.t("submitting"):C.t("confirmModify")),1)])),_:1},8,["onClick","disabled"])])),_:1},8,["class"])])),_:1})}],["__scopeId","data-v-602f98b0"]]);export{g as default};
