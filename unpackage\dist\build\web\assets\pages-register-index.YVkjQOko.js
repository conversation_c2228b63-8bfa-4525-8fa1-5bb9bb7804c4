import{s as e,f as t,o as a,h as i,w as o,j as s,m as l,z as r,u as n,x as c,i as d,v as h,A as m,t as p,p as u,k as f,I as g,y}from"./index-GgeQ7060.js";import{_ as w}from"./uni-icons.CMMZY0R_.js";import{r as C}from"./uni-app.es.COHFzCZB.js";import{_ as D}from"./uni-popup.DtPWGEwV.js";import{r as b}from"./request.DgxtsFy5.js";import{c as x}from"./index.B6QF5Ba_.js";import{_ as k}from"./_plugin-vue_export-helper.BCo6x5W8.js";import{t as v}from"./i18n.D3rYKUqN.js";const _=k({components:{SliderVerify:k({name:"SliderVerify",props:{verifyKey:{type:String,default:""}},data:()=>({bgImage:"",blockImage:"",blockY:0,sliderX:0,startX:0,originX:0,isMoving:!1,isReset:!1,maxMoveX:280,currentKey:""}),watch:{verifyKey:{immediate:!0,handler(e){e&&this.initVerify()}}},methods:{async initVerify(){try{const e=await b({url:"/api/auth/verify/get",method:"GET"});this.bgImage=e.data.bgImage,this.blockImage=e.data.blockImage,this.blockY=e.data.blockY,this.currentKey=e.data.key,this.sliderX=0,console.log("Got verify key:",this.currentKey)}catch(t){console.error("获取验证图片失败:",t),e({title:"初始化验证失败",icon:"none"})}},touchStart(e){this.isMoving=!0,this.startX=e.touches[0].pageX,this.originX=this.sliderX},touchMove(e){if(!this.isMoving)return;const t=e.touches[0].pageX-this.startX+this.originX;this.sliderX=Math.max(0,Math.min(t,this.maxMoveX))},async reset(){this.isReset=!0,this.sliderX=0,await new Promise((e=>setTimeout(e,300))),this.isReset=!1,await this.initVerify()},async touchEnd(){if(this.isMoving){this.isMoving=!1;try{console.log("Sending verify check - Key:",this.currentKey,"MoveX:",Math.round(this.sliderX));const e=await b({url:"/api/auth/verify/check",method:"POST",data:{moveX:Math.round(this.sliderX),key:this.currentKey}});e.data.success?this.$emit("success",e.data.verifyToken):(console.log("Verify failed, resetting..."),await this.reset())}catch(e){console.error("验证失败:",e),await this.reset()}}}}},[["render",function(e,m,p,u,f,g){const y=c,D=d,b=h,x=C(t("uni-icons"),w);return a(),i(D,{class:"slider-verify",onTouchmove:m[1]||(m[1]=n((()=>{}),["stop"]))},{default:o((()=>[s(D,{class:"verify-box"},{default:o((()=>[s(D,{class:"title"},{default:o((()=>[l(" 请完成安全验证 "),s(y,{class:"close",onClick:m[0]||(m[0]=t=>e.$emit("close"))},{default:o((()=>[l("×")])),_:1})])),_:1}),s(D,{class:"image-box"},{default:o((()=>[s(b,{src:f.bgImage,class:"bg-image",mode:"widthFix"},null,8,["src"]),s(b,{src:f.blockImage,class:"block-image",style:r({transform:`translate3d(${f.sliderX}px, ${f.blockY}px, 0)`,transition:f.isReset?"transform 0.3s":""})},null,8,["src","style"])])),_:1}),s(D,{class:"slider"},{default:o((()=>[s(D,{class:"slider-bar",style:r({width:f.sliderX+"px"})},null,8,["style"]),s(D,{class:"slider-button",style:r({transform:`translateX(${f.sliderX}px)`}),onTouchstart:n(g.touchStart,["stop","prevent"]),onTouchmove:n(g.touchMove,["stop","prevent"]),onTouchend:n(g.touchEnd,["stop","prevent"])},{default:o((()=>[s(x,{type:"arrowright",size:"20",color:"#666"})])),_:1},8,["style","onTouchstart","onTouchmove","onTouchend"])])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-1a171404"]])},data:()=>({statusBarHeight:0,showPhoneRegister:!1,registerType:"email",formData:{phone:"",email:"",name:"",password:"",confirmPassword:"",code:"",referrerCode:"",securityPassword:"",confirmSecurityPassword:"",emailCode:""},countdown:60,isCountingDown:!1,timer:null,isSubmitting:!1,captchaKey:"",captchaImg:"",inputCaptcha:"",emailCountdown:60,isEmailCountingDown:!1,emailTimer:null,isEmailSending:!1}),onLoad(e){this.statusBarHeight=0},computed:{countDownText(){return this.isCountingDown?`${this.countdown}s`:v("sendCode")},emailCountDownText(){return this.isEmailCountingDown?`${this.emailCountdown}s`:v("sendCode")}},methods:{t:v,closeCaptchaDialog(){this.$refs.captchaPopup.close()},async getCaptcha(){try{const t=await b({url:"/api/auth/captcha",method:"GET"});200===t.code?(this.captchaKey=t.data.key,this.captchaImg=t.data.image,this.inputCaptcha="",this.$refs.captchaPopup.open()):e({title:t.message||v("getCaptchaFail"),icon:"none"})}catch(t){e({title:v("getCaptchaImgFail"),icon:"none"})}},async sendSmsCode(){if(this.inputCaptcha)try{await b({url:"/api/auth/send-code-reg-okReg-2025",method:"POST",params:{phone:this.formData.phone,captchaKey:this.captchaKey,captchaCode:this.inputCaptcha}}),this.closeCaptchaDialog(),this.startCountDown(),this.isCountingDown=!0,e({title:v("codeSent"),icon:"success"})}catch(t){e({title:t.message||v("sendFail"),icon:"none"}),this.getCaptcha()}else e({title:v("captchaPlaceholder"),icon:"none"})},async sendCode(){if(this.formData.phone)if(/^1[3-9]\d{9}$/.test(this.formData.phone))try{if((await b({url:"/api/auth/check-phone",method:"GET",params:{phone:this.formData.phone}})).data)return void e({title:v("phoneRegistered"),icon:"none"});await this.getCaptcha()}catch(t){e({title:t.message||v("operateFail"),icon:"none"})}else e({title:v("inputPhoneValid"),icon:"none"});else e({title:v("inputPhone"),icon:"none"})},async handleRegister(){if(!this.isSubmitting){if("phone"===this.registerType){if(!this.formData.phone)return void e({title:v("inputPhone"),icon:"none"});if(!/^1[3-9]\d{9}$/.test(this.formData.phone))return void e({title:v("inputPhoneValid"),icon:"none"})}else{if(!this.formData.email)return void e({title:v("inputEmail"),icon:"none"});if(!/^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(this.formData.email))return void e({title:v("inputEmailValid"),icon:"none"})}if(this.formData.name)if(this.formData.name.length>8)e({title:v("inputNameMax"),icon:"none"});else if(this.formData.password)if(this.formData.password.length<6)e({title:v("pwdMinLen"),icon:"none"});else if(this.formData.confirmPassword)if(this.formData.password===this.formData.confirmPassword)if(this.formData.securityPassword)if(this.formData.securityPassword.length<6)e({title:v("securityPwdMinLen"),icon:"none"});else if(console.log("formData",this.formData),this.formData.referrerCode)try{this.isSubmitting=!0;const t={phone:this.formData.phone,email:this.formData.email,emailCode:this.formData.emailCode,username:this.formData.name,password:this.formData.password,code:this.formData.code,referrerCode:this.formData.referrerCode,securityPassword:this.formData.securityPassword};await b({url:"/api/auth/register",method:"POST",data:t});e({title:v("registerSuccess"),icon:"success"});this.formData.referrerCode;this.formData={phone:"",name:"",password:"",confirmPassword:"",code:"",referrerCode:"",securityPassword:""}}catch(t){console.error("注册失败:",t),e({title:t.message||v("registerFail"),icon:"none"})}finally{this.isSubmitting=!1}else e({title:v("inputReferrerCode"),icon:"none"});else e({title:v("inputSecurityPassword"),icon:"none"});else e({title:v("pwdNotMatch"),icon:"none"});else e({title:v("inputConfirmPassword"),icon:"none"});else e({title:v("inputPassword"),icon:"none"});else e({title:v("inputName"),icon:"none"})}},goToAgreement(e){const t=`${x.baseUrl}/#/pages/agreement/index?id=${e}&from=A`;window.location.href=t},validateName(e){if(/[^\u4e00-\u9fa5]/.test(e))return!1;const t=e.length;return t>0&&t<=5},handleNameInput(t){const a=t.detail.value;a.length>8?e({title:v("inputNameMax"),icon:"none"}):this.formData.name=a},startCountDown(){this.countdown=60,this.timer=setInterval((()=>{this.countdown>0?this.countdown--:(this.stopCountDown(),this.isCountingDown=!1)}),1e3)},stopCountDown(){clearInterval(this.timer),this.timer=null,this.isCountingDown=!1,this.countdown=60},handleBack(){m({url:"/pages/login/index"})},switchRegisterType(e){this.registerType=e,"phone"===e?this.formData.email="":"email"===e&&(this.formData.phone="")},async sendEmailCode(){if(this.formData.email)if(/^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(this.formData.email)){this.isEmailSending=!0;try{await b({url:"/api/auth/send-reg-code-email",method:"POST",params:{email:this.formData.email}}),e({title:v("codeSent"),icon:"success"}),this.startEmailCountDown()}catch(t){e({title:t.message||v("sendFail"),icon:"none"})}finally{this.isEmailSending=!1}}else e({title:v("inputEmailValid"),icon:"none"});else e({title:v("inputEmail"),icon:"none"})},startEmailCountDown(){this.emailCountdown=60,this.isEmailCountingDown=!0,this.emailTimer=setInterval((()=>{this.emailCountdown>0?this.emailCountdown--:(clearInterval(this.emailTimer),this.isEmailCountingDown=!1)}),1e3)}},beforeDestroy(){this.emailTimer&&clearInterval(this.emailTimer),this.timer&&clearInterval(this.timer)}},[["render",function(e,n,m,b,x,k){const v=C(t("uni-icons"),w),_=d,P=c,T=g,V=y,S=h,E=C(t("uni-popup"),D);return a(),i(_,{class:"register-container"},{default:o((()=>[s(_,{class:"custom-navbar",style:r({paddingTop:x.statusBarHeight+"px"})},{default:o((()=>[s(_,{class:"navbar-content"},{default:o((()=>[s(_,{class:"left-area",onClick:k.handleBack},{default:o((()=>[s(v,{type:"left",size:"20",color:"#fff"})])),_:1},8,["onClick"])])),_:1})])),_:1},8,["style"]),s(_,{class:"register-welcome"},{default:o((()=>[s(P,{class:"welcome-title"},{default:o((()=>[l(p(k.t("registerTitle")),1)])),_:1}),s(P,{class:"welcome-sub"},{default:o((()=>[l(p(k.t("registerWelcome")),1)])),_:1})])),_:1}),x.showPhoneRegister?(a(),i(_,{key:0,class:"register-type-tabs"},{default:o((()=>[s(_,{class:u(["tab","phone"===x.registerType?"active":""]),onClick:n[0]||(n[0]=e=>k.switchRegisterType("phone"))},{default:o((()=>[l(p(k.t("registerPhoneTab")),1)])),_:1},8,["class"]),s(_,{class:u(["tab","email"===x.registerType?"active":""]),onClick:n[1]||(n[1]=e=>k.switchRegisterType("email"))},{default:o((()=>[l(p(k.t("registerEmailTab")),1)])),_:1},8,["class"])])),_:1})):f("",!0),s(_,{class:u(["form-box",{"full-radius":!x.showPhoneRegister}])},{default:o((()=>[x.showPhoneRegister&&"phone"===x.registerType?(a(),i(_,{key:0,class:"input-item"},{default:o((()=>[s(v,{type:"phone",size:"20",color:"#ffd700"}),s(T,{type:"number",placeholder:k.t("inputPhone"),modelValue:x.formData.phone,"onUpdate:modelValue":n[2]||(n[2]=e=>x.formData.phone=e),"placeholder-style":"color: rgba(255, 255, 255, 0.5);"},null,8,["placeholder","modelValue"])])),_:1})):f("",!0),x.showPhoneRegister&&"email"!==x.registerType?f("",!0):(a(),i(_,{key:1,class:"input-item"},{default:o((()=>[s(v,{type:"email",size:"20",color:"#ffd700"}),s(T,{type:"text",placeholder:k.t("inputEmail"),modelValue:x.formData.email,"onUpdate:modelValue":n[3]||(n[3]=e=>x.formData.email=e),"placeholder-style":"color: rgba(255, 255, 255, 0.5);"},null,8,["placeholder","modelValue"])])),_:1})),x.showPhoneRegister&&"email"!==x.registerType?f("",!0):(a(),i(_,{key:2,class:"input-item code-item",style:{padding:"20rpx 10rpx"}},{default:o((()=>[s(v,{type:"auth",size:"20",color:"#ffd700"}),s(T,{modelValue:x.formData.emailCode,"onUpdate:modelValue":n[4]||(n[4]=e=>x.formData.emailCode=e),type:"text",placeholder:k.t("inputEmailCode"),autocomplete:"off",autocapitalize:"off",autocorrect:"off",spellcheck:"false",style:{flex:"1"}},null,8,["modelValue","placeholder"]),s(V,{class:"code-btn",onClick:k.sendEmailCode,disabled:x.isEmailCountingDown||x.isEmailSending},{default:o((()=>[l(p(x.isEmailSending?k.t("sending"):k.emailCountDownText),1)])),_:1},8,["onClick","disabled"])])),_:1})),s(_,{class:"input-item"},{default:o((()=>[s(v,{type:"person",size:"20",color:"#ffd700"}),s(T,{type:"text",placeholder:k.t("inputName"),modelValue:x.formData.name,"onUpdate:modelValue":n[5]||(n[5]=e=>x.formData.name=e),onInput:k.handleNameInput,"placeholder-style":"color: rgba(255, 255, 255, 0.5);"},null,8,["placeholder","modelValue","onInput"])])),_:1}),s(_,{class:"input-item"},{default:o((()=>[s(v,{type:"locked",size:"20",color:"#ffd700"}),s(T,{type:"password",placeholder:k.t("inputPassword"),modelValue:x.formData.password,"onUpdate:modelValue":n[6]||(n[6]=e=>x.formData.password=e),"placeholder-style":"color: rgba(255, 255, 255, 0.5);"},null,8,["placeholder","modelValue"])])),_:1}),s(_,{class:"input-item"},{default:o((()=>[s(v,{type:"locked",size:"20",color:"#ffd700"}),s(T,{type:"password",placeholder:k.t("inputConfirmPassword"),modelValue:x.formData.confirmPassword,"onUpdate:modelValue":n[7]||(n[7]=e=>x.formData.confirmPassword=e),"placeholder-style":"color: rgba(255, 255, 255, 0.5);"},null,8,["placeholder","modelValue"])])),_:1}),s(_,{class:"input-item"},{default:o((()=>[s(v,{type:"locked",size:"20",color:"#ffd700"}),s(T,{type:"password",placeholder:k.t("inputSecurityPassword"),modelValue:x.formData.securityPassword,"onUpdate:modelValue":n[8]||(n[8]=e=>x.formData.securityPassword=e),"placeholder-style":"color: rgba(255, 255, 255, 0.5);",maxlength:"15"},null,8,["placeholder","modelValue"])])),_:1}),s(_,{class:"input-item"},{default:o((()=>[s(v,{type:"paperplane",size:"20",color:"#ffd700"}),s(T,{type:"text",modelValue:x.formData.referrerCode,"onUpdate:modelValue":n[9]||(n[9]=e=>x.formData.referrerCode=e),placeholder:k.t("inputReferrerCode"),style:{color:"rgba(255, 255, 255, 0.6)"}},null,8,["modelValue","placeholder"])])),_:1}),s(V,{class:"submit-btn",onClick:k.handleRegister,disabled:x.isSubmitting},{default:o((()=>[l(p(x.isSubmitting?k.t("registering"):k.t("registerNow")),1)])),_:1},8,["onClick","disabled"])])),_:1},8,["class"]),s(E,{ref:"captchaPopup",type:"center","is-mask-click":!1},{default:o((()=>[s(_,{class:"captcha-dialog-content",style:{"background-color":"#222",padding:"40rpx","border-radius":"16rpx",width:"580rpx","box-sizing":"border-box"}},{default:o((()=>[s(_,{style:{"margin-bottom":"24rpx","font-size":"34rpx","text-align":"center",color:"#ffd700"}},{default:o((()=>[l(p(k.t("captchaTitle")),1)])),_:1}),s(_,{style:{display:"flex","align-items":"center","justify-content":"center","margin-bottom":"14rpx"}},{default:o((()=>[s(S,{src:x.captchaImg,onClick:k.getCaptcha,mode:"aspectFit",style:{width:"280rpx",height:"90rpx",cursor:"pointer","background-color":"#333"}},null,8,["src","onClick"]),s(P,{onClick:k.getCaptcha,style:{"margin-left":"20rpx",color:"#ffd700","font-size":"28rpx"}},{default:o((()=>[l(p(k.t("captchaRefresh")),1)])),_:1},8,["onClick"])])),_:1}),s(T,{modelValue:x.inputCaptcha,"onUpdate:modelValue":n[10]||(n[10]=e=>x.inputCaptcha=e),placeholder:k.t("captchaPlaceholder"),maxlength:"5",style:{border:"1px solid #444",padding:"20rpx",width:"100%",height:"100rpx","box-sizing":"border-box","border-radius":"8rpx","font-size":"32rpx","margin-bottom":"40rpx","text-align":"center",background:"#121212",color:"#ffd700"}},null,8,["modelValue","placeholder"]),s(_,{style:{display:"flex","justify-content":"space-between"}},{default:o((()=>[s(V,{onClick:k.sendSmsCode,size:"default",type:"primary",style:{width:"48%",background:"linear-gradient(90deg, #ffd700, #daa520)",color:"#121212"}},{default:o((()=>[l(p(k.t("captchaConfirm")),1)])),_:1},8,["onClick"]),s(V,{onClick:k.closeCaptchaDialog,size:"default",style:{width:"48%",background:"#333",color:"#fff"}},{default:o((()=>[l(p(k.t("captchaCancel")),1)])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1},512)])),_:1})}],["__scopeId","data-v-c805ab2e"]]);export{_ as default};
