import{H as a,n as t,e,s,f as o,h as c,w as l,i,o as n,j as r,m as u,z as d,t as f,p as h,l as p,q as _,F as m,k as y,x as b,y as T}from"./index-GgeQ7060.js";import{_ as g}from"./uni-icons.CMMZY0R_.js";import{r as v}from"./uni-app.es.COHFzCZB.js";import{C as k}from"./custom-navbar.Dp8ceLKT.js";import{r as x}from"./request.DgxtsFy5.js";import{_ as z}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.B6QF5Ba_.js";const w=z({components:{CustomNavbar:k},data:()=>({statusBarHeight:0,fundAvailable:"0",fundFrozen:"0",fundTotal:"0",copyAvailable:"0",copyFrozen:"0",copyTotal:"0",activeTab:1,fundList:[],copyList:[],fundPage:1,copyPage:1,fundHasMore:!0,copyHasMore:!0,pageSize:10}),computed:{currentList(){return 1===this.activeTab?this.fundList:this.copyList},hasMore(){return 1===this.activeTab?this.fundHasMore:this.copyHasMore}},created(){const t=a();this.statusBarHeight=t.statusBarHeight,this.getUserInfo(),this.loadTradeRecords()},methods:{handleBack(){t()},onTransfer(){e({url:"/pages/transfer/index"})},onWithdraw(){e({url:"/pages/withdraw/index"})},goToRecord(){e({url:"/pages/account-transfer/record"})},async getUserInfo(){try{const a=await x({url:"/api/user/info",method:"GET"});200===a.code&&a.data&&(this.fundAvailable=a.data.availableBalance||"0",this.fundFrozen=a.data.frozenBalance||"0",this.fundTotal=(Number(this.fundAvailable)+Number(this.fundFrozen)).toFixed(2),this.copyAvailable=a.data.copyTradeBalance||"0",this.copyFrozen=a.data.copyTradeFrozenBalance||"0",this.copyTotal=(Number(this.copyAvailable)+Number(this.copyFrozen)).toFixed(2))}catch(a){this.fundAvailable=this.fundFrozen=this.fundTotal="0",this.copyAvailable=this.copyFrozen=this.copyTotal="0"}},switchTab(a){this.activeTab!==a&&(this.activeTab=a,0===this.currentList.length&&this.loadTradeRecords())},async loadTradeRecords(){const a=this.activeTab,t=1===this.activeTab?this.fundPage:this.copyPage;try{const e=await x({url:"/api/trade-record/list",method:"GET",params:{page:t,size:this.pageSize,accountType:a}});if(200===e.code&&e.data){const a=e.data.records||[],s=e.data.current<e.data.pages;1===this.activeTab?(this.fundList=1===t?a:[...this.fundList,...a],this.fundHasMore=s):(this.copyList=1===t?a:[...this.copyList,...a],this.copyHasMore=s)}}catch(e){console.error("加载交易记录失败:",e),s({title:"加载失败",icon:"none"})}},loadMore(){1===this.activeTab?this.fundPage++:this.copyPage++,this.loadTradeRecords()},formatTime(a){if(!a)return"";const t=new Date(a),e=new Date-t;if(e<6e4)return"刚刚";if(e<36e5)return Math.floor(e/6e4)+"分钟前";if(e<864e5)return Math.floor(e/36e5)+"小时前";return`${(t.getMonth()+1).toString().padStart(2,"0")}-${t.getDate().toString().padStart(2,"0")} ${t.getHours().toString().padStart(2,"0")}:${t.getMinutes().toString().padStart(2,"0")}`}}},[["render",function(a,t,e,s,k,x){const z=v(o("uni-icons"),g),w=i,C=b,F=T;return n(),c(w,{class:"container"},{default:l((()=>[r(w,{class:"custom-navbar",style:d({paddingTop:k.statusBarHeight+"px"})},{default:l((()=>[r(w,{class:"navbar-content"},{default:l((()=>[r(w,{class:"left-area",onClick:x.handleBack},{default:l((()=>[r(z,{type:"left",size:"20",color:"#fff"})])),_:1},8,["onClick"]),r(C,{class:"page-title"},{default:l((()=>[u("我的资产")])),_:1}),r(w,{class:"right-area"},{default:l((()=>[r(C,{class:"record-link",onClick:x.goToRecord},{default:l((()=>[u("划转记录")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1},8,["style"]),r(w,{class:"asset-header"},{default:l((()=>[r(w,{class:"account-summary"},{default:l((()=>[r(w,{class:"account-block"},{default:l((()=>[r(C,{class:"account-title"},{default:l((()=>[u("资金账户")])),_:1}),r(w,{class:"account-row"},{default:l((()=>[r(C,null,{default:l((()=>[u("总余额：")])),_:1}),r(C,{class:"account-value"},{default:l((()=>[u("$"+f(k.fundTotal),1)])),_:1})])),_:1}),r(w,{class:"account-row"},{default:l((()=>[r(C,null,{default:l((()=>[u("可用：")])),_:1}),r(C,{class:"account-value"},{default:l((()=>[u("$"+f(k.fundAvailable),1)])),_:1}),r(C,{style:{"margin-left":"40rpx"}},{default:l((()=>[u("冻结：")])),_:1}),r(C,{class:"account-value"},{default:l((()=>[u("$"+f(k.fundFrozen),1)])),_:1})])),_:1})])),_:1}),r(w,{class:"account-block"},{default:l((()=>[r(C,{class:"account-title"},{default:l((()=>[u("跟单账户")])),_:1}),r(w,{class:"account-row"},{default:l((()=>[r(C,null,{default:l((()=>[u("总余额：")])),_:1}),r(C,{class:"account-value"},{default:l((()=>[u("$"+f(k.copyTotal),1)])),_:1})])),_:1})])),_:1})])),_:1}),r(w,{class:"btn-group"},{default:l((()=>[r(F,{class:"asset-btn",onClick:x.onTransfer},{default:l((()=>[u("互转")])),_:1},8,["onClick"]),r(F,{class:"asset-btn",onClick:x.onWithdraw},{default:l((()=>[u("提现")])),_:1},8,["onClick"])])),_:1})])),_:1}),r(w,{class:"transaction-card"},{default:l((()=>[r(w,{class:"section-title-row"},{default:l((()=>[r(C,{class:"section-title"},{default:l((()=>[u("我的交易")])),_:1})])),_:1}),r(w,{class:"tab-container"},{default:l((()=>[r(w,{class:h(["tab-item",{active:1===k.activeTab}]),onClick:t[0]||(t[0]=a=>x.switchTab(1))},{default:l((()=>[r(C,{class:"tab-text"},{default:l((()=>[u("资金账户")])),_:1})])),_:1},8,["class"]),r(w,{class:h(["tab-item",{active:2===k.activeTab}]),onClick:t[1]||(t[1]=a=>x.switchTab(2))},{default:l((()=>[r(C,{class:"tab-text"},{default:l((()=>[u("跟单账户")])),_:1})])),_:1},8,["class"])])),_:1}),x.currentList.length>0?(n(),c(w,{key:0,class:"transaction-list"},{default:l((()=>[(n(!0),p(m,null,_(x.currentList,((a,t)=>(n(),c(w,{class:"transaction-item",key:a.id},{default:l((()=>[r(w,{class:"transaction-left"},{default:l((()=>[r(C,{class:"transaction-type"},{default:l((()=>[u(f(a.tradeType),1)])),_:2},1024),r(C,{class:"transaction-time"},{default:l((()=>[u(f(x.formatTime(a.createTime)),1)])),_:2},1024)])),_:2},1024),r(w,{class:"transaction-right"},{default:l((()=>[r(C,{class:h(["transaction-amount",{income:a.amount>0,expense:a.amount<0}])},{default:l((()=>[u(f(a.amount>0?"+":"")+f(a.amount),1)])),_:2},1032,["class"]),r(C,{class:"transaction-remark"},{default:l((()=>[u(f(a.remark),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128)),x.hasMore?(n(),c(w,{key:0,class:"load-more",onClick:x.loadMore},{default:l((()=>[r(C,{class:"load-more-text"},{default:l((()=>[u("加载更多")])),_:1})])),_:1},8,["onClick"])):y("",!0)])),_:1})):(n(),c(w,{key:1,class:"empty-box"},{default:l((()=>[r(C,{class:"empty-text"},{default:l((()=>[u("暂无交易记录")])),_:1})])),_:1}))])),_:1})])),_:1})}],["__scopeId","data-v-327058c7"]]);export{w as default};
