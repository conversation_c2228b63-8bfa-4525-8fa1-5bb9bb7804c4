import{H as a,n as e,e as t,s,f as n,h as l,w as r,i as o,o as i,j as c,m as u,z as m,t as d,x as f,Y as h,I as p,y as b}from"./index-GgeQ7060.js";import{_ as g}from"./uni-icons.CMMZY0R_.js";import{r as _}from"./uni-app.es.COHFzCZB.js";import{r as T}from"./request.DgxtsFy5.js";import{_ as y}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.B6QF5Ba_.js";const v=y({data:()=>({statusBarHeight:0,accountTypes:["UID","邮箱"],accountTypeIndex:0,targetAccount:"",amount:"",availableAmount:"0",payPwd:"",remark:"",transferParams:{minTransfer:0,maxTransfer:0,transferFee:0,enableTransfer:!0},fee:0}),watch:{amount(a){this.calcFee()}},created(){const e=a();this.statusBarHeight=e.statusBarHeight,this.getTransferParams().then((()=>{this.getUserInfo()}))},methods:{handleBack(){e()},goToRecord(){t({url:"/pages/transfer/record"})},onAccountTypeChange(a){this.accountTypeIndex=a.detail.value,this.targetAccount=""},handleSubmit(){this.submitTransfer()},async getTransferParams(){try{const a=await T({url:"/api/sys/params/transfer-withdraw",method:"GET"});200===a.code&&a.data&&(this.transferParams=a.data)}catch(a){s({title:"获取转账参数失败",icon:"none"})}},async getUserInfo(){try{const a=await T({url:"/api/user/info",method:"GET"});200===a.code&&a.data&&(this.availableAmount=a.data.availableBalance||"0")}catch(a){this.availableAmount="0"}},calcFee(){const a=Number(this.amount),e=Number(this.transferParams.transferFee||0);isNaN(a)||isNaN(e)?this.fee=0:this.fee=e},async submitTransfer(){if(!this.targetAccount||!this.amount||!this.payPwd)return void s({title:"请填写完整信息",icon:"none"});if(!this.transferParams.enableTransfer)return void s({title:"当前不允许转账",icon:"none"});if(Number(this.amount)<Number(this.transferParams.minTransfer))return void s({title:`最小转账金额为${this.transferParams.minTransfer}`,icon:"none"});if(Number(this.amount)>Number(this.transferParams.maxTransfer))return void s({title:`最大转账金额为${this.transferParams.maxTransfer}`,icon:"none"});if(Number(this.amount)<=Number(this.fee))return void s({title:"金额需大于手续费",icon:"none"});let a={amount:this.amount,remark:this.remark,payPwd:this.payPwd};this.targetAccount.includes("@")?a.toEmail=this.targetAccount:a.toUid=this.targetAccount;try{const e=await T({url:"/api/transfer/create",method:"POST",data:a});200===e.code?(s({title:"转账成功",icon:"success"}),this.targetAccount="",this.amount="",this.remark="",this.payPwd="",this.getUserInfo()):s({title:e.msg||"转账失败",icon:"none"})}catch(e){s({title:e.message,icon:"none"})}}}},[["render",function(a,e,t,s,T,y){const v=_(n("uni-icons"),g),w=o,P=f,x=h,k=p,A=b;return i(),l(w,{class:"transfer-container"},{default:r((()=>[c(w,{class:"custom-navbar",style:m({paddingTop:T.statusBarHeight+"px"})},{default:r((()=>[c(w,{class:"navbar-content"},{default:r((()=>[c(w,{class:"left-area",onClick:y.handleBack},{default:r((()=>[c(v,{type:"left",size:"20",color:"#fff"})])),_:1},8,["onClick"]),c(P,{class:"page-title"},{default:r((()=>[u("互转")])),_:1}),c(w,{class:"right-area"},{default:r((()=>[c(P,{class:"record-link",onClick:y.goToRecord},{default:r((()=>[u("互转记录")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1},8,["style"]),c(w,{class:"scroll-content"},{default:r((()=>[c(w,{class:"form-section"},{default:r((()=>[c(w,{class:"form-item"},{default:r((()=>[c(P,{class:"label"},{default:r((()=>[u("账户类型")])),_:1}),c(x,{range:T.accountTypes,value:T.accountTypeIndex,onChange:y.onAccountTypeChange},{default:r((()=>[c(w,{class:"input select"},{default:r((()=>[u(d(T.accountTypes[T.accountTypeIndex]),1)])),_:1})])),_:1},8,["range","value","onChange"])])),_:1}),c(w,{class:"form-item"},{default:r((()=>[c(P,{class:"label"},{default:r((()=>[u("互转账号")])),_:1}),c(k,{class:"input",placeholder:1===T.accountTypeIndex?"请输入邮箱号":"请输入UID",modelValue:T.targetAccount,"onUpdate:modelValue":e[0]||(e[0]=a=>T.targetAccount=a)},null,8,["placeholder","modelValue"])])),_:1}),c(w,{class:"form-item amount-row"},{default:r((()=>[c(w,{class:"amount-label-row"},{default:r((()=>[c(P,{class:"label"},{default:r((()=>[u("金额")])),_:1}),c(P,{class:"available"},{default:r((()=>[u("可用："+d(T.availableAmount)+"USDT",1)])),_:1})])),_:1}),c(k,{class:"input",placeholder:"请输入金额",modelValue:T.amount,"onUpdate:modelValue":e[1]||(e[1]=a=>T.amount=a),type:"number"},null,8,["modelValue"])])),_:1}),c(w,{class:"form-item"},{default:r((()=>[c(P,{class:"label"},{default:r((()=>[u("支付密码")])),_:1}),c(k,{class:"input",placeholder:"请输入支付密码",modelValue:T.payPwd,"onUpdate:modelValue":e[2]||(e[2]=a=>T.payPwd=a),password:""},null,8,["modelValue"])])),_:1}),c(w,{class:"form-item"},{default:r((()=>[c(P,{class:"label"},{default:r((()=>[u("备注")])),_:1}),c(k,{class:"input",placeholder:"最多15字",modelValue:T.remark,"onUpdate:modelValue":e[3]||(e[3]=a=>T.remark=a),maxlength:"15"},null,8,["modelValue"])])),_:1}),c(w,{class:"result-action-row"},{default:r((()=>[c(w,{class:"result-info"},{default:r((()=>[c(P,{class:"result"},{default:r((()=>[u("到账数量："+d(T.amount&&null!==T.fee?(Number(T.amount)-Number(T.fee)).toFixed(2):0)+"USDT",1)])),_:1}),c(P,{class:"result"},{default:r((()=>[u("网络手续费："+d(T.fee)+"USDT",1)])),_:1})])),_:1}),c(A,{class:"submit-btn",onClick:y.handleSubmit},{default:r((()=>[u("确定")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-31295b8e"]]);export{v as default};
