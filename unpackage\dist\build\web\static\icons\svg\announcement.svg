<svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
  <circle cx="40" cy="40" r="40" fill="rgba(34, 209, 238, 0.1)">
    <animate attributeName="opacity" values="0.1;0.15;0.1" dur="2s" repeatCount="indefinite"/>
  </circle>
  
  <g transform="translate(12, 12)">
    <circle cx="28" cy="28" r="24" stroke="#22d1ee" stroke-width="2.5" opacity="0.2"/>
    
    <!-- 内部填充 -->
    <path d="M16 12h24v32H16z" fill="#22d1ee" opacity="0.1">
      <animate attributeName="opacity" values="0.1;0.2;0.1" dur="2s" repeatCount="indefinite"/>
    </path>
    
    <!-- 喇叭主体 -->
    <path d="M20 18l16-8v36l-16-8v-20z" stroke="#22d1ee" stroke-width="2.5" stroke-linecap="round">
      <animate attributeName="stroke-dasharray" values="0 100;100 100" dur="1s" fill="freeze"/>
    </path>
    
    <!-- 声波动画 -->
    <path d="M36 18c4 4 4 16 0 20" stroke="#22d1ee" stroke-width="2.5" stroke-linecap="round">
      <animate attributeName="d" 
               values="M36 18c4 4 4 16 0 20;M36 20c2 3 2 14 0 17;M36 18c4 4 4 16 0 20" 
               dur="2s" 
               repeatCount="indefinite"/>
    </path>
    <path d="M40 15c6 6 6 20 0 26" stroke="#22d1ee" stroke-width="2.5" stroke-linecap="round" opacity="0.6">
      <animate attributeName="d" 
               values="M40 15c6 6 6 20 0 26;M40 17c4 5 4 18 0 23;M40 15c6 6 6 20 0 26" 
               dur="2s" 
               repeatCount="indefinite"/>
    </path>
  </g>

  <circle cx="40" cy="40" r="38" stroke="url(#gradient)" stroke-width="2" opacity="0">
    <animate attributeName="opacity" values="0;0.6;0" dur="2s" begin="mouseover" fill="freeze"/>
  </circle>

  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#22d1ee;stop-opacity:0.5"/>
      <stop offset="100%" style="stop-color:#22d1ee;stop-opacity:0"/>
    </linearGradient>
  </defs>
</svg> 